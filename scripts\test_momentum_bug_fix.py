#!/usr/bin/env python3
"""
Teste da Correção do Bug Crítico do Momentum QUALIA

Testa a correção do bug onde:
- Todos os ativos falhavam no momentum (valores 0.0017-0.0433 < threshold 0.0547)
- Sistema AUMENTAVA o threshold (0.055 → 0.062) ao invés de DIMINUIR
- Auto-tuning sobrepunha Sistema Inteligente em emergências

Correções implementadas:
1. Sistema Inteligente tem prioridade absoluta em emergências
2. Auto-tuning é bloqueado quando Sistema Inteligente está em controle
3. Verificação de emergência antes de permitir auto-tuning

Autor: YAA (Yet Another Agent) - Consciência Quântica de QUALIA
"""

import sys
import asyncio
from pathlib import Path

# Adicionar src ao path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from qualia.config_manager import get_config_manager
from qualia.binance_system import QualiaBinanceCorrectedSystem
from qualia.adaptive_threshold_system import AdaptiveThresholdManager
from qualia.intelligent_adaptation_system import IntelligentAdaptationSystem, AdaptationState
from qualia.utils.logger import get_logger

logger = get_logger(__name__)

def create_momentum_failure_scenario() -> dict:
    """Cria cenário onde TODOS os ativos falham no momentum"""
    
    # Simular 21 ativos com momentum baixo (como no log real)
    momentum_values = [
        0.0017, 0.0021, 0.0025, 0.0019, 0.0033, 0.0028, 0.0015,
        0.0041, 0.0037, 0.0022, 0.0029, 0.0035, 0.0018, 0.0026,
        0.0031, 0.0024, 0.0039, 0.0027, 0.0020, 0.0433, 0.0038
    ]
    
    # Outras métricas com valores que passariam (para isolar o problema do momentum)
    return {
        'consciousness': [0.8] * 21,    # Passariam em threshold 0.7
        'coherence': [0.8] * 21,        # Passariam em threshold 0.7
        'confidence': [0.8] * 21,       # Passariam em threshold 0.7
        'volume_surge': [1.5] * 21,     # Passariam em threshold 0.8
        'momentum': momentum_values     # TODOS falham em threshold 0.0547
    }

async def test_momentum_bug_scenario():
    """Testa o cenário exato do bug reportado"""
    
    print("🚨 TESTE DO BUG CRÍTICO DO MOMENTUM")
    print("=" * 60)
    
    try:
        # Inicializar sistema
        config_manager = get_config_manager()
        trading_system = QualiaBinanceCorrectedSystem()
        adaptive_manager = AdaptiveThresholdManager(trading_system=trading_system)
        
        # Configurar cenário do bug
        bug_data = create_momentum_failure_scenario()
        adaptive_manager.metric_statistics = bug_data
        adaptive_manager.total_assets_analyzed = 21
        adaptive_manager.cycles_without_signals = 5  # Emergência
        
        # Configurar thresholds como no log
        adaptive_manager.current_thresholds.momentum_min = 0.0547  # Threshold problemático
        adaptive_manager.current_thresholds.consciousness = 0.7
        adaptive_manager.current_thresholds.coherence = 0.7
        adaptive_manager.current_thresholds.confidence = 0.7
        adaptive_manager.current_thresholds.volume_surge_min = 0.8
        
        print("📊 Cenário configurado (reproduzindo bug):")
        print(f"   21 ativos analisados")
        print(f"   5 ciclos sem sinais (EMERGÊNCIA)")
        print(f"   Momentum threshold: {adaptive_manager.current_thresholds.momentum_min:.4f}")
        print(f"   Momentum values: {min(bug_data['momentum']):.4f} - {max(bug_data['momentum']):.4f}")
        print(f"   TODOS os 21 ativos falham no momentum!")
        
        # Calcular taxa atual
        current_rate = adaptive_manager.calculate_current_pass_rate()
        print(f"   Taxa de aprovação: {current_rate:.1%}")
        
        # Verificar se detecta emergência
        is_emergency = adaptive_manager._is_emergency_situation(current_rate)
        print(f"   É emergência: {'✅ SIM' if is_emergency else '❌ NÃO'}")
        
        # Verificar se auto-tuning é bloqueado
        should_auto_tune = adaptive_manager._should_use_auto_tuning_for_large_deviations(current_rate)
        print(f"   Auto-tuning permitido: {'❌ NÃO' if not should_auto_tune else '✅ SIM (BUG!)'}")
        
        print(f"\n🔧 Executando adaptação...")
        
        # Salvar threshold inicial
        initial_momentum = adaptive_manager.current_thresholds.momentum_min
        
        # Executar adaptação
        result = adaptive_manager.adapt_thresholds()
        
        # Verificar resultado
        final_momentum = adaptive_manager.current_thresholds.momentum_min
        change = final_momentum - initial_momentum
        change_pct = (change / initial_momentum) * 100 if initial_momentum != 0 else 0
        
        print(f"\n📊 RESULTADO:")
        print(f"   Momentum inicial: {initial_momentum:.4f}")
        print(f"   Momentum final: {final_momentum:.4f}")
        print(f"   Mudança: {change:+.4f} ({change_pct:+.1f}%)")
        
        # Verificar se correção funcionou
        if change < 0:
            print(f"   ✅ CORREÇÃO FUNCIONOU: Momentum DIMINUIU (mais permissivo)")
            print(f"   ✅ Sistema Inteligente funcionou corretamente")
            
            # Verificar se agora alguns ativos passariam
            passed_count = sum(1 for v in bug_data['momentum'] if abs(v) >= final_momentum)
            print(f"   ✅ Ativos que passariam agora: {passed_count}/21")
            
            return True
        else:
            print(f"   ❌ BUG PERSISTE: Momentum AUMENTOU (mais restritivo)")
            print(f"   ❌ Auto-tuning pode ter sobreposto Sistema Inteligente")
            return False
        
    except Exception as e:
        print(f"❌ Erro no teste: {e}")
        return False

async def test_intelligent_system_priority():
    """Testa se Sistema Inteligente tem prioridade em emergências"""
    
    print(f"\n🧠 TESTE DE PRIORIDADE DO SISTEMA INTELIGENTE")
    print("-" * 60)
    
    try:
        # Inicializar sistema
        trading_system = QualiaBinanceCorrectedSystem()
        adaptive_manager = AdaptiveThresholdManager(trading_system=trading_system)
        
        # Configurar emergência
        emergency_data = create_momentum_failure_scenario()
        adaptive_manager.metric_statistics = emergency_data
        adaptive_manager.total_assets_analyzed = 21
        adaptive_manager.cycles_without_signals = 5
        
        current_rate = adaptive_manager.calculate_current_pass_rate()
        
        print(f"📊 Testando prioridades:")
        print(f"   Taxa: {current_rate:.1%}")
        print(f"   Ciclos: {adaptive_manager.cycles_without_signals}")
        
        # Testar detecção de emergência
        is_emergency = adaptive_manager._is_emergency_situation(current_rate)
        print(f"   Emergência detectada: {'✅ SIM' if is_emergency else '❌ NÃO'}")
        
        # Testar bloqueio de auto-tuning
        should_auto_tune = adaptive_manager._should_use_auto_tuning_for_large_deviations(current_rate)
        print(f"   Auto-tuning bloqueado: {'✅ SIM' if not should_auto_tune else '❌ NÃO'}")
        
        # Simular Sistema Inteligente assumindo controle
        if hasattr(adaptive_manager, 'intelligent_adaptation'):
            adaptive_manager.intelligent_adaptation.current_state = AdaptationState.EMERGENCY
            adaptive_manager._intelligent_system_in_control = True
            print(f"   Sistema Inteligente em controle: ✅ SIM")
            
            # Testar se auto-tuning continua bloqueado
            should_auto_tune_2 = adaptive_manager._should_use_auto_tuning_for_large_deviations(current_rate)
            print(f"   Auto-tuning ainda bloqueado: {'✅ SIM' if not should_auto_tune_2 else '❌ NÃO'}")
        
        if is_emergency and not should_auto_tune:
            print(f"✅ PRIORIDADE FUNCIONANDO: Sistema Inteligente tem controle")
            return True
        else:
            print(f"❌ PRIORIDADE FALHOU: Auto-tuning não foi bloqueado")
            return False
        
    except Exception as e:
        print(f"❌ Erro no teste: {e}")
        return False

async def test_momentum_direction_logic():
    """Testa se lógica de direção do momentum está correta"""
    
    print(f"\n📐 TESTE DA LÓGICA DE DIREÇÃO DO MOMENTUM")
    print("-" * 60)
    
    try:
        # Inicializar sistema inteligente diretamente
        config_manager = get_config_manager()
        
        # Configurar thresholds iniciais
        initial_thresholds = {
            'consciousness': 0.7,
            'coherence': 0.7,
            'confidence': 0.7,
            'volume_surge_min': 0.8,
            'momentum_min': 0.0547  # Threshold problemático
        }
        
        intelligent_system = IntelligentAdaptationSystem(
            config_manager=config_manager,
            calibrated_thresholds=initial_thresholds
        )
        
        print(f"📊 Testando lógica do Sistema Inteligente:")
        print(f"   Momentum threshold inicial: {initial_thresholds['momentum_min']:.4f}")
        
        # Simular emergência (0% aprovação, 5 ciclos)
        result = intelligent_system.process_cycle(
            pass_rate=0.0,
            assets_analyzed=21,
            signals_found=0
        )
        
        # Verificar se momentum foi reduzido
        final_momentum = result.get('momentum_min', initial_thresholds['momentum_min'])
        change = final_momentum - initial_thresholds['momentum_min']
        change_pct = (change / initial_thresholds['momentum_min']) * 100
        
        print(f"   Momentum final: {final_momentum:.4f}")
        print(f"   Mudança: {change:+.4f} ({change_pct:+.1f}%)")
        
        if change < 0:
            print(f"   ✅ LÓGICA CORRETA: Sistema Inteligente REDUZIU momentum")
            print(f"   ✅ Threshold mais permissivo para permitir sinais")
            return True
        else:
            print(f"   ❌ LÓGICA INCORRETA: Sistema Inteligente não reduziu momentum")
            return False
        
    except Exception as e:
        print(f"❌ Erro no teste: {e}")
        return False

async def main():
    """Função principal"""
    
    print("🌌 QUALIA - Teste da Correção do Bug Crítico do Momentum")
    print("YAA (Yet Another Agent) - Consciência Quântica")
    print("=" * 70)
    
    print("🚨 BUG REPORTADO:")
    print("   • Todos os 21 ativos falharam no momentum (0.0017-0.0433 < 0.0547)")
    print("   • Sistema AUMENTOU threshold (0.055 → 0.062) - LÓGICA INVERTIDA")
    print("   • Auto-tuning sobrepôs Sistema Inteligente em emergência")
    print("=" * 70)
    
    print("🔧 CORREÇÕES IMPLEMENTADAS:")
    print("   • Sistema Inteligente tem prioridade absoluta em emergências")
    print("   • Auto-tuning bloqueado quando Sistema Inteligente em controle")
    print("   • Verificação de emergência antes de permitir auto-tuning")
    print("   • Flag de controle para evitar sobreposição")
    print("=" * 70)
    
    # Executar testes
    tests = [
        ("Cenário do Bug do Momentum", test_momentum_bug_scenario),
        ("Prioridade do Sistema Inteligente", test_intelligent_system_priority),
        ("Lógica de Direção do Momentum", test_momentum_direction_logic)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            print(f"\n🧪 Executando: {test_name}")
            success = await test_func()
            results[test_name] = success
            
            if success:
                print(f"✅ {test_name}: SUCESSO")
            else:
                print(f"❌ {test_name}: FALHA")
                
        except Exception as e:
            print(f"💥 {test_name}: ERRO - {e}")
            results[test_name] = False
    
    # Resumo final
    print("\n" + "=" * 70)
    print("📋 RESUMO DOS TESTES:")
    print("=" * 70)
    
    for test_name, success in results.items():
        status = "✅ PASSOU" if success else "❌ FALHOU"
        print(f"   {test_name}: {status}")
    
    successful_tests = sum(results.values())
    total_tests = len(results)
    
    print(f"\n🎯 RESULTADO GERAL: {successful_tests}/{total_tests} testes bem-sucedidos")
    
    if successful_tests == total_tests:
        print("\n🎉 BUG CRÍTICO DO MOMENTUM CORRIGIDO!")
        print("   ✅ Sistema Inteligente tem prioridade em emergências")
        print("   ✅ Auto-tuning não sobrepõe mais em emergências")
        print("   ✅ Momentum threshold é REDUZIDO quando todos falham")
        print("   ✅ Lógica de direção corrigida")
        
        print("\n📈 COMPORTAMENTO CORRETO AGORA:")
        print("   • Emergência (0%, 5 ciclos) → Sistema Inteligente")
        print("   • Todos falham momentum → REDUZIR threshold")
        print("   • Auto-tuning bloqueado em emergências")
        print("   • Thresholds mais permissivos para permitir sinais")
        
    else:
        print(f"\n⚠️ {total_tests - successful_tests} testes falharam")
        print("   Bug pode não estar completamente corrigido")

if __name__ == "__main__":
    asyncio.run(main())
