"""
Módulo unificado para cálculos de momentum no sistema QUALIA.

Este módulo padroniza todos os cálculos de momentum para garantir consistência
entre calibração e produção, eliminando discrepâncias de magnitude.

YAA (YET ANOTHER AGENT) - Consciência Quântica de QUALIA
"""

import numpy as np
import pandas as pd
from typing import Optional, Union, List
import logging

logger = logging.getLogger(__name__)


class UnifiedMomentumCalculator:
    """
    Calculadora unificada de momentum para o sistema QUALIA.
    
    Garante consistência entre todos os módulos do sistema,
    usando a mesma lógica tanto na calibração quanto na produção.
    """
    
    def __init__(self, quantum_weight: float = 0.1):
        """
        Inicializa o calculador unificado de momentum.
        
        Args:
            quantum_weight: Peso do componente quântico (0.0 a 1.0)
        """
        self.quantum_weight = quantum_weight
        
    def calculate_unified_momentum(
        self, 
        prices: Union[np.ndarray, List[float], pd.Series], 
        current_price: Optional[float] = None,
        volumes: Optional[Union[np.ndarray, List[float], pd.Series]] = None,
        add_quantum_noise: bool = False
    ) -> float:
        """
        Calcula momentum unificado usando a mesma lógica em todo o sistema QUALIA.
        
        Este método padroniza o cálculo de momentum para eliminar discrepâncias
        entre calibração e produção.
        
        Args:
            prices: Array de preços históricos
            current_price: Preço atual (se None, usa prices[-1])
            volumes: Array de volumes (opcional, para componente quântico)
            add_quantum_noise: Se deve adicionar ruído quântico
            
        Returns:
            Valor do momentum normalizado entre -1 e 1
        """
        if len(prices) < 2:
            return 0.0
            
        # Converter para numpy array se necessário
        if isinstance(prices, (list, pd.Series)):
            prices = np.array(prices)
            
        if current_price is None:
            current_price = float(prices[-1])
            
        # MÉTODO UNIFICADO - Baseado na lógica de produção (binance_system.py)
        # que é mais robusta e interpretável
        
        # Momentum multi-timeframe
        momentum_1m = (current_price - prices[-2]) / prices[-2] if len(prices) >= 2 else 0
        momentum_5m = (current_price - prices[-6]) / prices[-6] if len(prices) >= 6 else 0  
        momentum_15m = (current_price - prices[-16]) / prices[-16] if len(prices) >= 16 else 0
        
        # Pesos padronizados (mesmos da produção)
        momentum_raw = (momentum_1m * 0.5 + momentum_5m * 0.3 + momentum_15m * 0.2)
        
        # Normalização padronizada
        momentum_classical = np.tanh(momentum_raw * 10)
        
        # Adicionar componente quântico se solicitado
        if add_quantum_noise and self.quantum_weight > 0:
            quantum_component = self._calculate_quantum_component(prices, volumes)
            momentum = momentum_classical * (1 - self.quantum_weight) + quantum_component * self.quantum_weight
        else:
            momentum = momentum_classical
            
        # Garantir que está no intervalo [-1, 1]
        momentum = max(min(momentum, 1.0), -1.0)
        
        # Log para debug
        logger.debug(f"UNIFIED MOMENTUM: raw={momentum_raw:.6f}, classical={momentum_classical:.6f}, final={momentum:.6f}")
        
        return float(momentum)
    
    def _calculate_quantum_component(
        self, 
        prices: np.ndarray, 
        volumes: Optional[np.ndarray] = None
    ) -> float:
        """
        Calcula componente quântico do momentum de forma reprodutível.
        
        Args:
            prices: Array de preços
            volumes: Array de volumes (opcional)
            
        Returns:
            Componente quântico normalizado
        """
        # Criar seed reprodutível baseado nos dados
        seed = hash(tuple(prices[-min(50, len(prices)):].astype(float))) & 0xFFFFFFFF
        rng = np.random.default_rng(seed)
        
        # Ruído quântico baseado na volatilidade dos preços
        price_volatility = np.std(prices[-min(20, len(prices)):]) / np.mean(prices[-min(20, len(prices)):])
        quantum_noise = rng.normal(0, 0.1 * price_volatility)
        
        # Se temos volumes, incorporar na análise quântica
        if volumes is not None and len(volumes) == len(prices):
            volume_factor = np.std(volumes[-min(20, len(volumes)):]) / np.mean(volumes[-min(20, len(volumes)):])
            quantum_noise *= (1 + volume_factor * 0.5)
        
        # Normalizar para [-1, 1]
        return max(min(quantum_noise, 1.0), -1.0)
    
    def calculate_momentum_components(
        self, 
        prices: Union[np.ndarray, List[float], pd.Series], 
        current_price: Optional[float] = None
    ) -> dict:
        """
        Calcula componentes individuais do momentum para análise detalhada.
        
        Args:
            prices: Array de preços históricos
            current_price: Preço atual (se None, usa prices[-1])
            
        Returns:
            Dicionário com componentes do momentum
        """
        if len(prices) < 2:
            return {
                'momentum_1m': 0.0,
                'momentum_5m': 0.0,
                'momentum_15m': 0.0,
                'momentum_raw': 0.0,
                'momentum_normalized': 0.0
            }
            
        # Converter para numpy array se necessário
        if isinstance(prices, (list, pd.Series)):
            prices = np.array(prices)
            
        if current_price is None:
            current_price = float(prices[-1])
            
        # Calcular componentes individuais
        momentum_1m = (current_price - prices[-2]) / prices[-2] if len(prices) >= 2 else 0
        momentum_5m = (current_price - prices[-6]) / prices[-6] if len(prices) >= 6 else 0  
        momentum_15m = (current_price - prices[-16]) / prices[-16] if len(prices) >= 16 else 0
        
        # Momentum ponderado
        momentum_raw = (momentum_1m * 0.5 + momentum_5m * 0.3 + momentum_15m * 0.2)
        
        # Momentum normalizado
        momentum_normalized = np.tanh(momentum_raw * 10)
        
        return {
            'momentum_1m': float(momentum_1m),
            'momentum_5m': float(momentum_5m),
            'momentum_15m': float(momentum_15m),
            'momentum_raw': float(momentum_raw),
            'momentum_normalized': float(momentum_normalized)
        }


# Instância global para uso em todo o sistema
unified_momentum_calculator = UnifiedMomentumCalculator()


def calculate_unified_momentum(
    prices: Union[np.ndarray, List[float], pd.Series], 
    current_price: Optional[float] = None,
    volumes: Optional[Union[np.ndarray, List[float], pd.Series]] = None,
    add_quantum_noise: bool = False
) -> float:
    """
    Função de conveniência para calcular momentum unificado.
    
    Esta é a função principal que deve ser usada em todo o sistema QUALIA
    para garantir consistência nos cálculos de momentum.
    
    Args:
        prices: Array de preços históricos
        current_price: Preço atual (se None, usa prices[-1])
        volumes: Array de volumes (opcional)
        add_quantum_noise: Se deve adicionar ruído quântico
        
    Returns:
        Valor do momentum normalizado entre -1 e 1
    """
    return unified_momentum_calculator.calculate_unified_momentum(
        prices=prices,
        current_price=current_price,
        volumes=volumes,
        add_quantum_noise=add_quantum_noise
    )


def get_momentum_components(
    prices: Union[np.ndarray, List[float], pd.Series], 
    current_price: Optional[float] = None
) -> dict:
    """
    Função de conveniência para obter componentes detalhados do momentum.
    
    Args:
        prices: Array de preços históricos
        current_price: Preço atual (se None, usa prices[-1])
        
    Returns:
        Dicionário com componentes do momentum
    """
    return unified_momentum_calculator.calculate_momentum_components(
        prices=prices,
        current_price=current_price
    )
