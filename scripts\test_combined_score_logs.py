#!/usr/bin/env python3
"""
Teste dos Logs de Combined Score QUALIA

Testa se os logs de análise detalhada incluem corretamente o combined_score:
1. Verificar se MetricAnalysis inclui combined_score
2. Testar cálculo do combined_score
3. Validar logs de análise detalhada
4. Verificar threshold de aprovação (0.65)
5. Testar diferentes cenários (PASS/FAIL)

Autor: YAA (Yet Another Agent) - Consciência Quântica de QUALIA
"""

import sys
import asyncio
import io
import logging
from pathlib import Path

# Adicionar src ao path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from qualia.config_manager import get_config_manager
from qualia.binance_system import QualiaBinanceCorrectedSystem
from qualia.adaptive_threshold_system import AdaptiveThresholdManager, MetricAnalysis
from qsi.qualia.utils.logger import get_logger

logger = get_logger(__name__)

async def test_combined_score_calculation():
    """Testa se o combined_score é calculado corretamente"""
    
    print("🧮 TESTE DO CÁLCULO DO COMBINED_SCORE")
    print("-" * 60)
    
    try:
        # Inicializar sistema
        trading_system = QualiaBinanceCorrectedSystem()
        adaptive_manager = AdaptiveThresholdManager(trading_system=trading_system)
        
        # Configurar thresholds conhecidos
        adaptive_manager.current_thresholds.consciousness = 0.70
        adaptive_manager.current_thresholds.coherence = 0.65
        adaptive_manager.current_thresholds.confidence = 0.60
        adaptive_manager.current_thresholds.volume_surge_min = 1.0
        adaptive_manager.current_thresholds.momentum_min = 0.005
        
        print("📊 Thresholds configurados:")
        print(f"   Consciousness: {adaptive_manager.current_thresholds.consciousness:.3f}")
        print(f"   Coherence: {adaptive_manager.current_thresholds.coherence:.3f}")
        print(f"   Confidence: {adaptive_manager.current_thresholds.confidence:.3f}")
        print(f"   Volume surge: {adaptive_manager.current_thresholds.volume_surge_min:.3f}")
        print(f"   Momentum: {adaptive_manager.current_thresholds.momentum_min:.4f}")
        
        # Testar diferentes cenários
        test_cases = [
            {
                'name': 'Alto Score (deve PASSAR)',
                'metrics': {
                    'consciousness': 0.85,
                    'coherence': 0.80,
                    'confidence': 0.75,
                    'volume_surge': 1.5,
                    'momentum': 0.010
                },
                'expected_pass': True
            },
            {
                'name': 'Score Médio (pode PASSAR ou FALHAR)',
                'metrics': {
                    'consciousness': 0.72,
                    'coherence': 0.68,
                    'confidence': 0.62,
                    'volume_surge': 1.1,
                    'momentum': 0.006
                },
                'expected_pass': None  # Depende do cálculo
            },
            {
                'name': 'Baixo Score (deve FALHAR)',
                'metrics': {
                    'consciousness': 0.50,
                    'coherence': 0.45,
                    'confidence': 0.40,
                    'volume_surge': 0.8,
                    'momentum': 0.002
                },
                'expected_pass': False
            }
        ]
        
        print(f"\n🔍 Testando cálculo do combined_score:")
        all_correct = True
        
        for test_case in test_cases:
            print(f"\n   {test_case['name']}:")
            
            # Criar análise
            analysis = adaptive_manager.analyze_metric_vs_threshold(
                test_case['metrics'], 
                'TEST/USDT'
            )
            
            print(f"     Combined Score: {analysis.combined_score:.3f}")
            print(f"     Threshold passed: {'✅ SIM' if analysis.threshold_passed else '❌ NÃO'}")
            print(f"     Score >= 0.65: {'✅ SIM' if analysis.combined_score >= 0.65 else '❌ NÃO'}")
            
            # Verificar expectativa
            if test_case['expected_pass'] is not None:
                score_passes = analysis.combined_score >= 0.65
                if score_passes != test_case['expected_pass']:
                    print(f"     ⚠️ Resultado inesperado!")
                    all_correct = False
                else:
                    print(f"     ✅ Resultado esperado")
        
        if all_correct:
            print(f"\n✅ Cálculo do combined_score funcionando corretamente")
            return True
        else:
            print(f"\n❌ Problemas no cálculo do combined_score")
            return False
        
    except Exception as e:
        print(f"❌ Erro no teste: {e}")
        return False

async def test_log_output():
    """Testa se os logs incluem o combined_score corretamente"""
    
    print(f"\n📝 TESTE DOS LOGS DE COMBINED_SCORE")
    print("-" * 60)
    
    try:
        # Inicializar sistema
        trading_system = QualiaBinanceCorrectedSystem()
        adaptive_manager = AdaptiveThresholdManager(trading_system=trading_system)
        
        # Criar análises de teste
        test_analyses = [
            MetricAnalysis(
                symbol="BTC/USDT",
                consciousness=0.85,
                coherence=0.80,
                confidence=0.75,
                volume_surge=1.5,
                momentum=0.010,
                threshold_passed=True,
                failed_thresholds=[],
                quality_score=0.80,
                combined_score=0.78  # Score alto - deve PASSAR
            ),
            MetricAnalysis(
                symbol="ETH/USDT",
                consciousness=0.60,
                coherence=0.55,
                confidence=0.50,
                volume_surge=0.9,
                momentum=0.003,
                threshold_passed=False,
                failed_thresholds=["consciousness", "coherence"],
                quality_score=0.55,
                combined_score=0.45  # Score baixo - deve FALHAR
            ),
            MetricAnalysis(
                symbol="ADA/USDT",
                consciousness=0.72,
                coherence=0.68,
                confidence=0.65,
                volume_surge=1.2,
                momentum=0.007,
                threshold_passed=True,
                failed_thresholds=[],
                quality_score=0.68,
                combined_score=0.67  # Score médio - deve PASSAR
            )
        ]
        
        print("📊 Análises de teste criadas:")
        for analysis in test_analyses:
            status = "PASS" if analysis.combined_score >= 0.65 else "FAIL"
            print(f"   {analysis.symbol}: Combined Score {analysis.combined_score:.3f} ({status})")
        
        # Capturar logs
        print(f"\n📝 Capturando logs de análise detalhada...")
        
        log_capture = io.StringIO()
        handler = logging.StreamHandler(log_capture)
        handler.setLevel(logging.INFO)
        
        # Adicionar handler temporário
        root_logger = logging.getLogger()
        root_logger.addHandler(handler)
        
        try:
            # Executar log de análise detalhada
            adaptive_manager.log_detailed_analysis(test_analyses)
            
            # Obter logs capturados
            log_output = log_capture.getvalue()
            
        finally:
            # Remover handler
            root_logger.removeHandler(handler)
        
        # Verificar se logs contêm combined_score
        print(f"\n🔍 Verificando conteúdo dos logs:")
        
        checks = [
            ("Combined Score presente", "Combined Score:" in log_output),
            ("Threshold 0.65 presente", "0.65 threshold" in log_output),
            ("Status PASS presente", "PASS vs" in log_output),
            ("Status FAIL presente", "FAIL vs" in log_output),
            ("BTC/USDT presente", "BTC/USDT" in log_output),
            ("ETH/USDT presente", "ETH/USDT" in log_output),
            ("ADA/USDT presente", "ADA/USDT" in log_output)
        ]
        
        all_checks_passed = True
        for check_name, check_result in checks:
            status = "✅ SIM" if check_result else "❌ NÃO"
            print(f"   {check_name}: {status}")
            if not check_result:
                all_checks_passed = False
        
        # Mostrar trecho dos logs para debug
        print(f"\n📄 Trecho dos logs capturados:")
        lines = log_output.split('\n')
        combined_score_lines = [line for line in lines if 'Combined Score:' in line]
        
        if combined_score_lines:
            print("   Linhas com Combined Score encontradas:")
            for line in combined_score_lines[:3]:  # Mostrar até 3 linhas
                print(f"     {line.strip()}")
        else:
            print("   ❌ Nenhuma linha com 'Combined Score:' encontrada")
            print(f"   📄 Primeiras 10 linhas dos logs:")
            for line in lines[:10]:
                if line.strip():
                    print(f"     {line.strip()}")
        
        if all_checks_passed and combined_score_lines:
            print(f"\n✅ Logs incluem combined_score corretamente")
            return True
        else:
            print(f"\n❌ Logs não incluem combined_score adequadamente")
            return False
        
    except Exception as e:
        print(f"❌ Erro no teste: {e}")
        return False

async def test_threshold_accuracy():
    """Testa se o threshold usado nos logs está correto"""
    
    print(f"\n🎯 TESTE DA PRECISÃO DO THRESHOLD")
    print("-" * 60)
    
    try:
        # Inicializar sistema
        trading_system = QualiaBinanceCorrectedSystem()
        adaptive_manager = AdaptiveThresholdManager(trading_system=trading_system)
        
        # Verificar threshold do config
        config_threshold = trading_system.config.get('signal_quality_filters', {}).get('min_combined_score', 0.65)
        print(f"📊 Threshold do config: {config_threshold:.2f}")
        
        # Criar análise de teste
        test_analysis = MetricAnalysis(
            symbol="TEST/USDT",
            consciousness=0.70,
            coherence=0.65,
            confidence=0.60,
            volume_surge=1.0,
            momentum=0.005,
            threshold_passed=True,
            failed_thresholds=[],
            quality_score=0.65,
            combined_score=0.66  # Ligeiramente acima do threshold
        )
        
        # Capturar logs
        log_capture = io.StringIO()
        handler = logging.StreamHandler(log_capture)
        root_logger = logging.getLogger()
        root_logger.addHandler(handler)
        
        try:
            adaptive_manager.log_detailed_analysis([test_analysis])
            log_output = log_capture.getvalue()
        finally:
            root_logger.removeHandler(handler)
        
        # Verificar se threshold correto aparece nos logs
        threshold_in_logs = f"{config_threshold:.2f} threshold" in log_output
        combined_score_in_logs = f"{test_analysis.combined_score:.3f}" in log_output
        
        print(f"🔍 Verificações:")
        print(f"   Threshold {config_threshold:.2f} nos logs: {'✅ SIM' if threshold_in_logs else '❌ NÃO'}")
        print(f"   Combined score {test_analysis.combined_score:.3f} nos logs: {'✅ SIM' if combined_score_in_logs else '❌ NÃO'}")
        
        if threshold_in_logs and combined_score_in_logs:
            print(f"✅ Threshold nos logs está correto")
            return True
        else:
            print(f"❌ Threshold nos logs está incorreto")
            print(f"📄 Logs capturados:")
            for line in log_output.split('\n'):
                if 'Combined Score:' in line:
                    print(f"   {line.strip()}")
            return False
        
    except Exception as e:
        print(f"❌ Erro no teste: {e}")
        return False

async def main():
    """Função principal"""
    
    print("🌌 QUALIA - Teste dos Logs de Combined Score")
    print("YAA (Yet Another Agent) - Consciência Quântica")
    print("=" * 70)
    
    print("🎯 FUNCIONALIDADE IMPLEMENTADA:")
    print("   • Campo combined_score adicionado à MetricAnalysis")
    print("   • Cálculo usando mesma lógica do sistema de trading")
    print("   • Logs mostram: Combined Score: 0.XXX (PASS/FAIL vs 0.65 threshold)")
    print("   • Auditoria completa de cada decisão de aprovação/rejeição")
    print("=" * 70)
    
    # Executar testes
    tests = [
        ("Cálculo do Combined Score", test_combined_score_calculation),
        ("Logs de Combined Score", test_log_output),
        ("Precisão do Threshold", test_threshold_accuracy)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            print(f"\n🧪 Executando: {test_name}")
            success = await test_func()
            results[test_name] = success
            
            if success:
                print(f"✅ {test_name}: SUCESSO")
            else:
                print(f"❌ {test_name}: FALHA")
                
        except Exception as e:
            print(f"💥 {test_name}: ERRO - {e}")
            results[test_name] = False
    
    # Resumo final
    print("\n" + "=" * 70)
    print("📋 RESUMO DOS TESTES:")
    print("=" * 70)
    
    for test_name, success in results.items():
        status = "✅ PASSOU" if success else "❌ FALHOU"
        print(f"   {test_name}: {status}")
    
    successful_tests = sum(results.values())
    total_tests = len(results)
    
    print(f"\n🎯 RESULTADO GERAL: {successful_tests}/{total_tests} testes bem-sucedidos")
    
    if successful_tests == total_tests:
        print("\n🎉 LOGS DE COMBINED_SCORE FUNCIONANDO!")
        print("   ✅ MetricAnalysis inclui combined_score")
        print("   ✅ Cálculo usando lógica do sistema de trading")
        print("   ✅ Logs mostram combined_score com threshold")
        print("   ✅ Auditoria completa implementada")
        
        print("\n📈 FORMATO DOS LOGS AGORA:")
        print("   PASS BTC/USDT:")
        print("     Consciousness: 0.850 (PASS)")
        print("     Coherence:     0.800 (PASS)")
        print("     Confidence:    0.750 (PASS)")
        print("     Volume Surge:  1.500 (PASS)")
        print("     Momentum:      0.0100 (PASS)")
        print("     Quality Score: 0.800")
        print("     Combined Score: 0.780 (PASS vs 0.65 threshold)")
        
        print("\n🔍 BENEFÍCIOS:")
        print("   • Rastreamento completo de cada decisão")
        print("   • Debug facilitado de aprovações/rejeições")
        print("   • Auditoria do critério final (combined_score)")
        print("   • Visibilidade do threshold atual (0.65)")
        
    else:
        print(f"\n⚠️ {total_tests - successful_tests} testes falharam")
        print("   Implementação pode precisar de ajustes")

if __name__ == "__main__":
    asyncio.run(main())
