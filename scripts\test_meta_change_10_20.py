#!/usr/bin/env python3
"""
Teste da Mudança de Meta de Aprovação QUALIA

Testa se a mudança de meta de "15-20%" para "10-20%" foi aplicada corretamente:
1. Parâmetros do algoritmo target-driven search
2. Condições de detecção de desvios
3. Mensagens de log
4. Documentação e comentários
5. Comportamento do sistema com nova meta

Autor: YAA (Yet Another Agent) - Consciência Quântica de QUALIA
"""

import sys
import asyncio
import numpy as np
from pathlib import Path

# Adicionar src ao path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from qualia.config_manager import get_config_manager
from qualia.binance_system import QualiaBinanceCorrectedSystem
from qualia.adaptive_threshold_system import AdaptiveThresholdManager
from qsi.qualia.utils.logger import get_logger

logger = get_logger(__name__)

def create_test_data_12_percent() -> dict:
    """Cria dados que resultam em ~12% de aprovação (dentro da nova meta)"""
    np.random.seed(42)
    
    # Dados calibrados para ~12% de aprovação
    return {
        'consciousness': [0.6] * 12 + [0.4] * 88,  # 12% passam em 0.6
        'coherence': [0.7] * 15 + [0.3] * 85,      # 15% passam em 0.7
        'confidence': [0.65] * 18 + [0.3] * 82,    # 18% passam em 0.65
        'volume_surge': [1.0] * 20 + [0.5] * 80,   # 20% passam em 0.9
        'momentum': [0.008] * 25 + [0.001] * 75    # 25% passam em 0.005
    }

async def test_target_driven_parameters():
    """Testa se parâmetros do target-driven foram atualizados"""
    
    print("🎯 TESTE DOS PARÂMETROS TARGET-DRIVEN")
    print("-" * 60)
    
    try:
        # Inicializar sistema
        trading_system = QualiaBinanceCorrectedSystem()
        adaptive_manager = AdaptiveThresholdManager(trading_system=trading_system)
        
        # Verificar parâmetros padrão do tune_to_target
        print("📊 Verificando parâmetros padrão do tune_to_target...")
        
        # Simular dados
        test_data = create_test_data_12_percent()
        adaptive_manager.metric_statistics = test_data
        adaptive_manager.total_assets_analyzed = 100
        
        # Função de avaliação simples para teste
        def simple_eval(metric_name: str, threshold_value: float) -> float:
            values = test_data.get(metric_name, [])
            if not values:
                return 0.0
            passed = sum(1 for v in values if v >= threshold_value)
            return passed / len(values)
        
        # Testar tune_to_target com parâmetros padrão
        print(f"   Testando tune_to_target com parâmetros padrão...")
        
        result = adaptive_manager.tune_to_target(
            metric_name='consciousness',
            start=0.8,
            direction='down',
            eval_fn=simple_eval
            # Não especificar target_low e target_high para usar padrões
        )
        
        # Verificar se resultado está na nova faixa (10-20%)
        final_rate = simple_eval('consciousness', result)
        print(f"   Threshold otimizado: {result:.4f}")
        print(f"   Taxa resultante: {final_rate:.1%}")
        
        if 0.10 <= final_rate <= 0.20:
            print(f"   ✅ Parâmetros padrão corretos (meta 10-20%)")
            return True
        else:
            print(f"   ❌ Parâmetros incorretos - taxa fora da meta 10-20%")
            return False
        
    except Exception as e:
        print(f"❌ Erro no teste: {e}")
        return False

async def test_deviation_detection():
    """Testa se detecção de desvios foi atualizada"""
    
    print(f"\n🔍 TESTE DA DETECÇÃO DE DESVIOS")
    print("-" * 60)
    
    try:
        # Inicializar sistema
        trading_system = QualiaBinanceCorrectedSystem()
        adaptive_manager = AdaptiveThresholdManager(trading_system=trading_system)
        adaptive_manager.total_assets_analyzed = 50
        
        # Testar diferentes taxas
        test_cases = [
            (0.05, "Muito baixa", True),   # 5% - deve acionar auto-tuning
            (0.12, "Normal", False),       # 12% - dentro da nova meta, não deve acionar
            (0.18, "Normal", False),       # 18% - dentro da nova meta, não deve acionar
            (0.30, "Muito alta", True),    # 30% - deve acionar auto-tuning
        ]
        
        print("📊 Testando detecção de desvios:")
        all_correct = True
        
        for rate, description, should_trigger in test_cases:
            # Resetar flag para cada teste
            adaptive_manager._auto_tuning_executed_this_cycle = False
            
            should_auto_tune = adaptive_manager._should_use_auto_tuning_for_large_deviations(rate)
            
            status = "✅" if should_auto_tune == should_trigger else "❌"
            print(f"   {rate:.0%} ({description}): {status} {'Aciona' if should_auto_tune else 'Não aciona'} auto-tuning")
            
            if should_auto_tune != should_trigger:
                all_correct = False
        
        if all_correct:
            print(f"\n✅ Detecção de desvios atualizada corretamente")
            print(f"   • Taxa 10-20%: Considerada normal")
            print(f"   • Taxa <8% ou >25%: Aciona auto-tuning")
            return True
        else:
            print(f"\n❌ Detecção de desvios incorreta")
            return False
        
    except Exception as e:
        print(f"❌ Erro no teste: {e}")
        return False

async def test_system_behavior():
    """Testa comportamento do sistema com taxa de 12% (nova meta)"""
    
    print(f"\n🎭 TESTE DO COMPORTAMENTO DO SISTEMA")
    print("-" * 60)
    
    try:
        # Inicializar sistema
        trading_system = QualiaBinanceCorrectedSystem()
        adaptive_manager = AdaptiveThresholdManager(trading_system=trading_system)
        
        # Configurar dados que resultam em ~12% (dentro da nova meta)
        test_data = create_test_data_12_percent()
        adaptive_manager.metric_statistics = test_data
        adaptive_manager.total_assets_analyzed = 100
        
        print("📊 Cenário configurado:")
        print(f"   Dados: 100 amostras")
        print(f"   Taxa esperada: ~12% (dentro da nova meta 10-20%)")
        
        # Calcular taxa atual
        current_rate = adaptive_manager.calculate_current_pass_rate()
        print(f"   Taxa real: {current_rate:.1%}")
        
        # Verificar se sistema considera normal
        is_emergency = adaptive_manager._is_emergency_situation(current_rate)
        should_auto_tune = adaptive_manager._should_use_auto_tuning_for_large_deviations(current_rate)
        
        print(f"\n🔍 Análise do sistema:")
        print(f"   É emergência: {'❌ NÃO' if not is_emergency else '✅ SIM (ERRO!)'}")
        print(f"   Deve usar auto-tuning: {'❌ NÃO' if not should_auto_tune else '✅ SIM (ERRO!)'}")
        
        # Verificar se está na faixa correta
        in_target_range = 0.10 <= current_rate <= 0.20
        print(f"   Está na meta 10-20%: {'✅ SIM' if in_target_range else '❌ NÃO'}")
        
        if in_target_range and not is_emergency and not should_auto_tune:
            print(f"\n✅ COMPORTAMENTO CORRETO:")
            print(f"   • Taxa 12% considerada normal")
            print(f"   • Sistema não aciona correções desnecessárias")
            print(f"   • Nova meta 10-20% funcionando")
            return True
        else:
            print(f"\n❌ COMPORTAMENTO INCORRETO:")
            print(f"   • Sistema deveria considerar 12% como normal")
            return False
        
    except Exception as e:
        print(f"❌ Erro no teste: {e}")
        return False

async def test_log_messages():
    """Testa se mensagens de log foram atualizadas"""
    
    print(f"\n📝 TESTE DAS MENSAGENS DE LOG")
    print("-" * 60)
    
    try:
        # Inicializar sistema
        trading_system = QualiaBinanceCorrectedSystem()
        adaptive_manager = AdaptiveThresholdManager(trading_system=trading_system)
        
        # Simular análises para gerar logs
        from qualia.adaptive_threshold_system import MetricAnalysis
        
        analyses = [
            MetricAnalysis(
                symbol=f"TEST{i}/USDT",
                consciousness=0.7,
                coherence=0.6,
                confidence=0.65,
                volume_surge=1.2,
                momentum=0.01,
                threshold_passed=(i < 15),  # 15% passam
                failed_thresholds=[],
                quality_score=0.65,
                combined_score=0.70
            )
            for i in range(100)
        ]
        
        print("📊 Simulando log de análise detalhada...")
        
        # Capturar logs (método simplificado)
        import io
        import logging
        
        # Criar handler para capturar logs
        log_capture = io.StringIO()
        handler = logging.StreamHandler(log_capture)
        logger.addHandler(handler)
        
        # Executar método que gera logs
        adaptive_manager.log_detailed_analysis(analyses)
        
        # Obter logs capturados
        log_output = log_capture.getvalue()
        
        # Verificar se contém nova meta
        contains_new_meta = "META: 10-20%" in log_output
        not_contains_old_meta = "META: 15-20%" not in log_output
        
        print(f"   Contém 'META: 10-20%': {'✅ SIM' if contains_new_meta else '❌ NÃO'}")
        print(f"   Não contém 'META: 15-20%': {'✅ SIM' if not_contains_old_meta else '❌ NÃO'}")
        
        # Cleanup
        logger.removeHandler(handler)
        
        if contains_new_meta and not_contains_old_meta:
            print(f"✅ Mensagens de log atualizadas corretamente")
            return True
        else:
            print(f"❌ Mensagens de log não foram atualizadas")
            return False
        
    except Exception as e:
        print(f"❌ Erro no teste: {e}")
        return False

async def main():
    """Função principal"""
    
    print("🌌 QUALIA - Teste da Mudança de Meta 15-20% → 10-20%")
    print("YAA (Yet Another Agent) - Consciência Quântica")
    print("=" * 70)
    
    print("🎯 MUDANÇA IMPLEMENTADA:")
    print("   • Meta anterior: 15-20% de aprovação")
    print("   • Meta nova: 10-20% de aprovação")
    print("   • Objetivo: Ser menos restritiva, aceitar a partir de 10%")
    print("=" * 70)
    
    # Executar testes
    tests = [
        ("Parâmetros Target-Driven", test_target_driven_parameters),
        ("Detecção de Desvios", test_deviation_detection),
        ("Comportamento do Sistema", test_system_behavior),
        ("Mensagens de Log", test_log_messages)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            print(f"\n🧪 Executando: {test_name}")
            success = await test_func()
            results[test_name] = success
            
            if success:
                print(f"✅ {test_name}: SUCESSO")
            else:
                print(f"❌ {test_name}: FALHA")
                
        except Exception as e:
            print(f"💥 {test_name}: ERRO - {e}")
            results[test_name] = False
    
    # Resumo final
    print("\n" + "=" * 70)
    print("📋 RESUMO DOS TESTES:")
    print("=" * 70)
    
    for test_name, success in results.items():
        status = "✅ PASSOU" if success else "❌ FALHOU"
        print(f"   {test_name}: {status}")
    
    successful_tests = sum(results.values())
    total_tests = len(results)
    
    print(f"\n🎯 RESULTADO GERAL: {successful_tests}/{total_tests} testes bem-sucedidos")
    
    if successful_tests == total_tests:
        print("\n🎉 MUDANÇA DE META IMPLEMENTADA COM SUCESSO!")
        print("   ✅ Parâmetros target-driven atualizados (target_low=0.10)")
        print("   ✅ Detecção de desvios ajustada (<8% ou >25%)")
        print("   ✅ Taxa 10-20% considerada normal")
        print("   ✅ Mensagens de log atualizadas")
        print("   ✅ Sistema menos restritivo")
        
        print("\n📈 COMPORTAMENTO ESPERADO AGORA:")
        print("   • Taxa 5-7%: Muito baixa → Auto-tuning")
        print("   • Taxa 10-20%: Normal → Sistema Inteligente")
        print("   • Taxa 25-30%: Muito alta → Auto-tuning")
        print("   • Faixa aceitável expandida de 5% para 10%")
        
    else:
        print(f"\n⚠️ {total_tests - successful_tests} testes falharam")
        print("   Algumas mudanças podem não ter sido aplicadas corretamente")

if __name__ == "__main__":
    asyncio.run(main())
