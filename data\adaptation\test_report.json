{"system_status": {"enabled": true, "current_state": "emergency", "time_since_last_adaptation_hours": 1.1958333333333332e-06, "cycles_since_last_adaptation": 69}, "current_metrics": {"pass_rate": 0.15, "cycles_without_signals": 0, "total_assets_analyzed": 50, "consecutive_low_rate_cycles": 0, "consecutive_high_rate_cycles": 0}, "thresholds": {"calibrated": {"consciousness": 0.854, "coherence": 0.82, "confidence": 0.719, "volume_surge_min": 0.856, "momentum_min": 0.008}, "current": {"consciousness": 0.683, "coherence": 0.656, "confidence": 0.575, "volume_surge_min": 0.642, "momentum_min": 0.006}, "deviations": {"consciousness": {"absolute": -0.17099999999999993, "percentage": -20.023419203747064}, "coherence": {"absolute": -0.16399999999999992, "percentage": -19.999999999999993}, "confidence": {"absolute": -0.14400000000000002, "percentage": -20.0278164116829}, "volume_surge_min": {"absolute": -0.21399999999999997, "percentage": -24.999999999999996}, "momentum_min": {"absolute": -0.002, "percentage": -25.0}}}, "adaptation_history": {"total_adaptations": 6, "recent_adaptations": [{"timestamp": "2025-07-22T16:40:22.089732", "reason": "manual", "state_before": "emergency", "state_after": "calibrated", "thresholds_before": {"consciousness": 0.683, "coherence": 0.656, "confidence": 0.575, "volume_surge_min": 0.642, "momentum_min": 0.006}, "thresholds_after": {"consciousness": 0.854, "coherence": 0.82, "confidence": 0.719, "volume_surge_min": 0.856, "momentum_min": 0.008}, "pass_rate_before": 0.02, "cycles_without_signals": 0, "adjustment_percentage": 0.0, "notes": "Teste cenário 2"}, {"timestamp": "2025-07-22T16:40:22.092434", "reason": "high_pass_rate", "state_before": "calibrated", "state_after": "permissive", "thresholds_before": {"consciousness": 0.854, "coherence": 0.82, "confidence": 0.719, "volume_surge_min": 0.856, "momentum_min": 0.008}, "thresholds_after": {"consciousness": 0.95, "coherence": 0.943, "confidence": 0.827, "volume_surge_min": 0.984, "momentum_min": 0.009}, "pass_rate_before": 0.4, "cycles_without_signals": 0, "adjustment_percentage": 0.15, "notes": "Adaptação automática: high_pass_rate"}, {"timestamp": "2025-07-22T16:40:22.092434", "reason": "manual", "state_before": "permissive", "state_after": "calibrated", "thresholds_before": {"consciousness": 0.95, "coherence": 0.943, "confidence": 0.827, "volume_surge_min": 0.984, "momentum_min": 0.009}, "thresholds_after": {"consciousness": 0.854, "coherence": 0.82, "confidence": 0.719, "volume_surge_min": 0.856, "momentum_min": 0.008}, "pass_rate_before": 0.4, "cycles_without_signals": 0, "adjustment_percentage": 0.0, "notes": "Teste cenário 3"}, {"timestamp": "2025-07-22T16:40:22.105824", "reason": "emergency", "state_before": "calibrated", "state_after": "emergency", "thresholds_before": {"consciousness": 0.854, "coherence": 0.82, "confidence": 0.719, "volume_surge_min": 0.856, "momentum_min": 0.008}, "thresholds_after": {"consciousness": 0.683, "coherence": 0.656, "confidence": 0.575, "volume_surge_min": 0.642, "momentum_min": 0.006}, "pass_rate_before": 0.0, "cycles_without_signals": 3, "adjustment_percentage": -0.25, "notes": "Adaptação automática: emergency"}, {"timestamp": "2025-07-22T16:40:22.113521", "reason": "emergency", "state_before": "emergency", "state_after": "emergency", "thresholds_before": {"consciousness": 0.683, "coherence": 0.656, "confidence": 0.575, "volume_surge_min": 0.642, "momentum_min": 0.006}, "thresholds_after": {"consciousness": 0.683, "coherence": 0.656, "confidence": 0.575, "volume_surge_min": 0.642, "momentum_min": 0.006}, "pass_rate_before": 0.0, "cycles_without_signals": 6, "adjustment_percentage": -0.25, "notes": "Adaptação automática: emergency"}]}}