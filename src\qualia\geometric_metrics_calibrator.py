"""
Sistema de Calibração Unificado de Métricas QUALIA
Calibra TODOS os thresholds baseado em simulação histórica e performance real
Inclui: consciousness, coherence, confidence, volume_surge_min, momentum_min
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional
from datetime import datetime, timedelta, timezone
import asyncio
from dataclasses import dataclass
import json
from pathlib import Path

from .config_manager import get_config_manager
from .binance_system import QualiaBinanceCorrectedSystem
from .utils.logger import get_logger
from .calibration.cache_manager import IntelligentCacheManager
from .calibration.parallel_processor import ParallelCalibrationProcessor
from .calibration.validation_system import CalibrationValidationSystem

logger = get_logger(__name__)

@dataclass
class CalibrationPoint:
    """Ponto de calibração com métricas e resultado"""
    timestamp: datetime
    symbol: str
    consciousness: float
    coherence: float
    confidence: float
    volume_surge: float
    momentum: float
    future_return_1h: float
    future_return_4h: float
    was_profitable: bool

@dataclass
class CalibrationResults:
    """Resultados da calibração"""
    symbol: str
    total_points: int
    profitable_points: int
    success_rate: float
    recommended_thresholds: Dict[str, float]
    percentile_analysis: Dict[str, Dict[str, float]]

class QualiaMetricsCalibrator:
    """Calibrador unificado de TODAS as métricas QUALIA baseado em simulação histórica"""

    def __init__(self, trading_system: QualiaBinanceCorrectedSystem,
                 enable_cache: bool = True,
                 enable_parallel: bool = True,
                 enable_validation: bool = True):
        self.trading_system = trading_system
        self.config_manager = get_config_manager()
        # Ressonador geométrico removido
        self.calibration_data: List[CalibrationPoint] = []
        self.results_cache: Dict[str, CalibrationResults] = {}

        # Melhorias estruturais
        self.enable_cache = enable_cache
        self.enable_parallel = enable_parallel
        self.enable_validation = enable_validation

        # Inicializar componentes das melhorias estruturais
        if self.enable_cache:
            self.cache_manager = IntelligentCacheManager()
            logger.info("Cache inteligente ativado")

        if self.enable_parallel:
            self.parallel_processor = ParallelCalibrationProcessor()
            logger.info("Processamento paralelo ativado")

        if self.enable_validation:
            self.validation_system = CalibrationValidationSystem()
            logger.info("Sistema de validação ativado")

    # Funções de scoring replicadas de binance_system.py para simulação precisa
    def _sigmoid_gate(self, value: float, threshold: float, steepness: float = 5.0) -> float:
        import math
        normalized = (value - threshold) / threshold if threshold > 0 else value
        try:
            return 1.0 / (1.0 + math.exp(-steepness * normalized))
        except OverflowError:
            return 1.0 if normalized > 0 else 0.0

    def _linear_gate(self, value: float, threshold: float) -> float:
        if value >= threshold:
            excess = value - threshold
            max_excess = 1.0 - threshold
            return 0.5 + 0.5 * (excess / max_excess) if max_excess > 0 else 1.0
        else:
            return 0.5 * (value / threshold) if threshold > 0 else 0.0

    def _get_recent_momentum_history(self, symbol: str, lookback_periods: int = 50) -> List[float]:
        """Obtém histórico recente de momentum usando o sistema principal."""
        try:
            return self.trading_system._get_recent_momentum_history(symbol, lookback_periods)
        except Exception as e:  # pragma: no cover - fallback em caso de erro
            logger.warning(f"Falha ao obter histórico de momentum para {symbol}: {e}")
            return []

    def _normalize_momentum_zscore(self, momentum: float, symbol: str) -> float:
        import numpy as np
        momentum_history = self._get_recent_momentum_history(symbol)
        if len(momentum_history) < 10: return abs(momentum) / 0.01
        median_mom = np.median(momentum_history)
        mad_mom = np.median(np.abs(np.array(momentum_history) - median_mom))
        if mad_mom < 1e-6: mad_mom = np.std(momentum_history)
        if mad_mom < 1e-6: mad_mom = 0.01
        return (momentum - median_mom) / mad_mom

    def _calculate_flexible_score(self, point: 'CalibrationPoint', thresholds: Dict[str, float]) -> float:
        momentum_z = self._normalize_momentum_zscore(point.momentum, point.symbol)
        volume_score = self._sigmoid_gate(point.volume_surge, thresholds['volume_surge_min'], steepness=5.0)
        momentum_score = self._sigmoid_gate(abs(momentum_z), 2.0, steepness=3.0)
        consciousness_score = self._linear_gate(point.consciousness, thresholds['consciousness'])
        coherence_score = self._linear_gate(point.coherence, thresholds['coherence'])
        confidence_score = self._linear_gate(point.confidence, thresholds['confidence'])
        weights = {'consciousness': 0.30, 'coherence': 0.25, 'confidence': 0.25, 'volume': 0.10, 'momentum': 0.10}
        return (weights['consciousness'] * consciousness_score +
                weights['coherence'] * coherence_score +
                weights['confidence'] * confidence_score +
                weights['volume'] * volume_score +
                weights['momentum'] * momentum_score)
        
    async def calibrate_all_assets(self,
                                 days_back: int = 120,  # Aumentado para 120 dias (mais histórico)
                                 profit_threshold: float = 0.012,  # Corrigido para 1.2% (alinhado com TP real)
                                 time_horizon_hours: int = 4,
                                 regime_aware: bool = True,
                                 fast_window_days: int = 3,
                                 slow_window_days: int = 30,
                                 enable_validation: bool = True,
                                 enable_parallel: bool = True) -> Dict[str, CalibrationResults]:
        """
        Calibra TODAS as métricas QUALIA para todos os ativos monitorados

        Args:
            days_back: Dias de histórico para análise (padrão: 120 dias)
            profit_threshold: Threshold de lucro para considerar sucesso (1.2% = 0.012, alinhado com TP real)
            time_horizon_hours: Horizonte temporal para verificar lucro
            regime_aware: Usar calibração regime-aware (normal, crash, pump)
            fast_window_days: Janela rápida para peso temporal adaptativo
            slow_window_days: Janela lenta para peso temporal adaptativo
            enable_validation: Usar validação cruzada
            enable_parallel: Usar processamento paralelo
        """
        logger.info(" Iniciando calibração UNIFICADA de métricas QUALIA...")
        logger.info(f"   Período: {days_back} dias | Lucro mínimo: {profit_threshold:.1%} | Horizonte: {time_horizon_hours}h")
        logger.info("   Métricas: consciousness, coherence, confidence, volume_surge, momentum")
        logger.info(f"   Regime-aware: {regime_aware} | Validação: {enable_validation} | Paralelo: {enable_parallel}")

        # Configurar peso temporal adaptativo
        tau_days = 2.0  # Half-life de 2 dias
        w_short = 1 - np.exp(-fast_window_days / tau_days)
        logger.info(f"   Peso temporal adaptativo: w_short={w_short:.3f} (fast={fast_window_days}d, slow={slow_window_days}d)")
        
        results = {}
        assets = self.trading_system.all_assets
        
        for i, symbol in enumerate(assets, 1):
            logger.info(f" Calibrando {symbol} ({i}/{len(assets)})...")
            
            try:
                result = await self._calibrate_single_asset(
                    symbol, days_back, profit_threshold, time_horizon_hours, regime_aware
                )
                results[symbol] = result
                
                logger.info(f" {symbol}: {result.success_rate:.1%} sucesso "
                          f"({result.profitable_points}/{result.total_points} pontos)")
                
            except Exception as e:
                logger.error(f" Erro calibrando {symbol}: {e}")
                continue
                
            # Pequena pausa para não sobrecarregar API
            await asyncio.sleep(0.5)
        
        # Calcular thresholds agregados
        aggregated_thresholds = self._calculate_aggregated_thresholds(results)
        
        # Salvar resultados
        await self._save_calibration_results(results, aggregated_thresholds)
        
        logger.info(" Calibração concluída!")
        return results
    
    async def _calibrate_single_asset(self,
                                    symbol: str,
                                    days_back: int,
                                    profit_threshold: float,
                                    time_horizon_hours: int,
                                    regime_aware: bool = True) -> CalibrationResults:
        """Calibra métricas para um ativo específico usando dados históricos REAIS"""

        # 1. Obter dados históricos REAIS da API Binance
        end_time = datetime.now(timezone.utc)
        start_time = end_time - timedelta(days=days_back)

        logger.info(f" Obtendo dados históricos REAIS para {symbol}: {start_time.date()} a {end_time.date()}")

        # Usar método de dados históricos reais do sistema
        df = await self._fetch_historical_data(symbol, start_time, end_time)

        if df is None or len(df) < (days_back * 24):  # Mínimo de candles esperados (1h timeframe)
            raise ValueError(f"Dados históricos insuficientes para {symbol}: {len(df) if df is not None else 0} candles")

        logger.info(f" {symbol}: {len(df)} candles históricos obtidos ({df.index[0]} a {df.index[-1]})")
        calibration_points = []
        
        # 2. Simular cálculo de métricas em cada ponto
        for i in range(len(df) - time_horizon_hours - 1):
            try:
                # Simular market_data para este ponto temporal
                historical_market_data = {
                    'symbol': symbol,
                    'ohlcv_df': df.iloc[:i+20],  # Usar dados até este ponto
                    'price': df['close'].iloc[i],
                    'spread': 0.001,  # Estimativa
                    'volume_24h': df['volume'].iloc[i] * 24
                }
                
                # Calcular métricas como o sistema faria
                quantum_metrics = self.trading_system.calculate_enhanced_quantum_metrics(historical_market_data)
                if not quantum_metrics:
                    continue
                
                # Calcular retorno futuro usando triple-barrier method
                current_price = df['close'].iloc[i]
                future_price_1h = df['close'].iloc[i + 1] if i + 1 < len(df) else current_price

                # Triple-barrier labeling com TP=1.2% e SL=0.8% (valores reais do sistema)
                tp_pct = 0.012  # 1.2% take profit
                sl_pct = 0.008  # 0.8% stop loss
                was_profitable, actual_return, hold_time = self._calculate_triple_barrier_outcome(
                    df, i, tp_pct, sl_pct, time_horizon_hours
                )

                future_return_1h = (future_price_1h - current_price) / current_price
                future_return_4h = actual_return  # Usar retorno real do triple-barrier
                
                # Criar ponto de calibração
                point = CalibrationPoint(
                    timestamp=df.index[i],
                    symbol=symbol,
                    consciousness=quantum_metrics['consciousness'],
                    coherence=quantum_metrics['coherence'],
                    confidence=quantum_metrics['confidence'],
                    volume_surge=quantum_metrics.get('volume_surge', 1.0),
                    momentum=quantum_metrics['momentum'],
                    future_return_1h=future_return_1h,
                    future_return_4h=future_return_4h,
                    was_profitable=was_profitable
                )
                
                calibration_points.append(point)
                
            except Exception as e:
                logger.debug(f"Erro processando ponto {i} para {symbol}: {e}")
                continue
        
        # 3. Analisar pontos lucrativos
        profitable_points = [p for p in calibration_points if p.was_profitable]
        
        if len(profitable_points) < 10:
            logger.warning(f"Poucos pontos lucrativos para {symbol}: {len(profitable_points)}")
        
        # 4. Calcular percentis das métricas dos pontos lucrativos
        percentile_analysis = self._calculate_percentiles(profitable_points)
        
        # 5. Otimizar thresholds usando objetivo composto
        logger.info(f" Otimizando thresholds para {symbol} usando objetivo composto...")
        if regime_aware:
            recommended_thresholds = self._optimize_thresholds_by_regime(calibration_points, percentile_analysis)
        else:
            recommended_thresholds = self._optimize_thresholds_f1(calibration_points, percentile_analysis)
        
        return CalibrationResults(
            symbol=symbol,
            total_points=len(calibration_points),
            profitable_points=len(profitable_points),
            success_rate=len(profitable_points) / len(calibration_points) if calibration_points else 0,
            recommended_thresholds=recommended_thresholds,
            percentile_analysis=percentile_analysis
        )

    def _calculate_triple_barrier_outcome(self, df: pd.DataFrame, entry_idx: int,
                                        tp_pct: float, sl_pct: float,
                                        max_hold_hours: int) -> tuple:
        """
        Calcula resultado usando triple-barrier method (TP/SL/timeout)

        Args:
            df: DataFrame com dados OHLCV
            entry_idx: Índice de entrada
            tp_pct: Percentual de take profit (ex: 0.008 = 0.8%)
            sl_pct: Percentual de stop loss (ex: 0.015 = 1.5%)
            max_hold_hours: Máximo de horas para manter posição

        Returns:
            tuple: (was_profitable: bool, return_pct: float, hold_time: int)
        """
        if entry_idx >= len(df) - 1:
            return False, 0.0, 0

        entry_price = df['close'].iloc[entry_idx]
        tp_price = entry_price * (1 + tp_pct)
        sl_price = entry_price * (1 - sl_pct)

        # Verificar cada candle subsequente
        for i in range(entry_idx + 1, min(entry_idx + max_hold_hours + 1, len(df))):
            high = df['high'].iloc[i]
            low = df['low'].iloc[i]

            # Verificar se bateu TP primeiro (assumindo que TP é checado antes de SL)
            if high >= tp_price:
                return_pct = (tp_price - entry_price) / entry_price
                return True, return_pct, i - entry_idx

            # Verificar se bateu SL
            if low <= sl_price:
                return_pct = (sl_price - entry_price) / entry_price
                return False, return_pct, i - entry_idx

        # Timeout - usar preço de fechamento do último candle
        final_idx = min(entry_idx + max_hold_hours, len(df) - 1)
        final_price = df['close'].iloc[final_idx]
        return_pct = (final_price - entry_price) / entry_price
        hold_time = final_idx - entry_idx

        return return_pct > 0, return_pct, hold_time

    def _optimize_thresholds_f1(self, calibration_points: List[CalibrationPoint],
                               percentile_analysis: Dict) -> Dict[str, float]:
        """
        Otimiza thresholds maximizando F1-score em vez de usar percentis fixos

        Args:
            calibration_points: Lista de pontos de calibração
            percentile_analysis: Análise de percentis como fallback

        Returns:
            Dict com thresholds otimizados
        """
        try:
            from sklearn.metrics import f1_score
            from sklearn.model_selection import ParameterGrid
            import numpy as np

            if len(calibration_points) < 20:
                logger.warning(f"Poucos pontos para otimização F1 ({len(calibration_points)}), usando percentis")
                return self._get_percentile_thresholds(percentile_analysis)

            # Extrair features e labels
            features = []
            labels = []

            for point in calibration_points:
                features.append([
                    point.consciousness,
                    point.coherence,
                    point.confidence,
                    point.volume_surge,
                    abs(point.momentum)
                ])
                labels.append(point.was_profitable)

            features = np.array(features)
            labels = np.array(labels)

            # Definir grid de busca baseado nos percentis como referência
            p10_vals = [percentile_analysis[metric]['p10'] for metric in ['consciousness', 'coherence', 'confidence', 'volume_surge', 'momentum']]
            p50_vals = [percentile_analysis[metric]['p50'] for metric in ['consciousness', 'coherence', 'confidence', 'volume_surge', 'momentum']]
            p90_vals = [percentile_analysis[metric]['p90'] for metric in ['consciousness', 'coherence', 'confidence', 'volume_surge', 'momentum']]

            param_grid = {
                'consciousness': np.linspace(max(0.3, p10_vals[0]), min(0.98, p90_vals[0]), 15),
                'coherence': np.linspace(max(0.4, p10_vals[1]), min(0.99, p90_vals[1]), 15),
                'confidence': np.linspace(max(0.2, p10_vals[2]), min(0.95, p90_vals[2]), 15),
                'volume_surge_min': np.linspace(max(0.5, p10_vals[3]), min(3.0, p90_vals[3]), 15),
                'momentum_min': np.linspace(max(0.01, p10_vals[4]), min(0.3, p90_vals[4]), 15),
                'min_combined_score': np.linspace(0.4, 0.85, 10)  # Otimizar min_combined_score
            }

            best_f1 = 0
            best_thresholds = self._get_percentile_thresholds(percentile_analysis)  # Fallback

            logger.info(f" Testando {len(list(ParameterGrid(param_grid)))} combinações de thresholds...")

            # Grid search com amostragem para performance
            grid_sample_size = min(1000, len(list(ParameterGrid(param_grid))))
            param_combinations = list(ParameterGrid(param_grid))[:grid_sample_size]

            for i, params in enumerate(param_combinations):
                if i % 200 == 0:
                    logger.debug(f" Progresso otimização: {i}/{len(param_combinations)}")

                # Aplicar thresholds
                predictions = self._apply_thresholds_to_points(calibration_points, params)

                # Calcular objetivo composto (F1 + PnL + Trade Count)
                try:
                    composite_score = self._calculate_composite_objective(
                        labels, predictions, calibration_points
                    )

                    if composite_score > best_f1:  # Reutilizando variável best_f1 como best_score
                        best_f1 = composite_score
                        best_thresholds = params.copy()

                except Exception as e:
                    logger.debug(f"Erro calculando objetivo composto para params {params}: {e}")
                    continue

            logger.info(f" Otimização concluída: Score composto = {best_f1:.3f}")
            return best_thresholds

        except ImportError:
            logger.warning("sklearn não disponível, usando percentis como fallback")
            return self._get_percentile_thresholds(percentile_analysis)
        except Exception as e:
            logger.error(f"Erro na otimização F1: {e}")
            return self._get_percentile_thresholds(percentile_analysis)

    def _get_percentile_thresholds(self, percentile_analysis: Dict) -> Dict[str, float]:
        """Fallback para thresholds baseados em percentis"""
        return {
            'consciousness': percentile_analysis['consciousness']['p20'],
            'coherence': percentile_analysis['coherence']['p20'],
            'confidence': percentile_analysis['confidence']['p20'],
            'volume_surge_min': percentile_analysis.get('volume_surge', {}).get('p25', 1.0),
            'momentum_min': percentile_analysis['momentum']['p15'],
            'min_combined_score': 0.65  # Valor padrão do sistema
        }

    def _apply_thresholds_to_points(self, calibration_points: List[CalibrationPoint],
                                   thresholds: Dict[str, float]) -> List[bool]:
        """Aplica thresholds aos pontos de calibração e retorna predições"""
        predictions = []

        for point in calibration_points:
            passes_all = True

            # Verificar cada threshold individual
            if point.consciousness < thresholds.get('consciousness', 0):
                passes_all = False
            if point.coherence < thresholds.get('coherence', 0):
                passes_all = False
            if point.confidence < thresholds.get('confidence', 0):
                passes_all = False
            if point.volume_surge < thresholds.get('volume_surge_min', 0):
                passes_all = False
            if abs(point.momentum) < thresholds.get('momentum_min', 0):
                passes_all = False

            # Verificar min_combined_score se especificado
            if 'min_combined_score' in thresholds:
                combined_score = self._calculate_flexible_score(point, thresholds)
                if combined_score < thresholds['min_combined_score']:
                    passes_all = False

            predictions.append(passes_all)

        return predictions

    def _calculate_composite_objective(self, labels: List[bool], predictions: List[bool],
                                     calibration_points: List[CalibrationPoint]) -> float:
        """
        Calcula objetivo composto: α * F1 + β * PnL_médio + γ * trade_count_normalizado

        Args:
            labels: Labels reais
            predictions: Predições do modelo
            calibration_points: Pontos de calibração para calcular PnL

        Returns:
            float: Score composto normalizado
        """
        from sklearn.metrics import f1_score
        import numpy as np

        # Pesos do objetivo composto
        alpha = 0.5   # Peso do F1-score
        beta = 0.3    # Peso do PnL médio
        gamma = 0.2   # Peso da contagem de trades

        # 1. F1-score (0-1)
        f1 = f1_score(labels, predictions, zero_division=0)

        # 2. PnL médio dos trades selecionados (normalizado)
        selected_points = [point for point, pred in zip(calibration_points, predictions) if pred]
        if selected_points:
            returns = [point.future_return_4h for point in selected_points]
            avg_return = np.mean(returns)
            # Normalizar: 0% = 0.5, +2% = 1.0, -2% = 0.0
            pnl_score = max(0, min(1, 0.5 + avg_return * 25))  # 25 = 1/(0.04/2)
        else:
            pnl_score = 0.0

        # 3. Trade count normalizado (evita overfitting para poucos trades)
        total_trades = len(calibration_points)
        selected_trades = sum(predictions)
        if total_trades > 0:
            trade_ratio = selected_trades / total_trades
            # Penalizar muito poucos (<5%) ou muitos trades (>50%)
            if trade_ratio < 0.05:
                trade_score = trade_ratio * 10  # 0-0.5
            elif trade_ratio > 0.5:
                trade_score = 1.0 - (trade_ratio - 0.5)  # 1.0-0.5
            else:
                trade_score = 0.5 + (trade_ratio - 0.05) * (0.5 / 0.45)  # 0.5-1.0
        else:
            trade_score = 0.0

        # Combinar scores
        composite_score = alpha * f1 + beta * pnl_score + gamma * trade_score

        return composite_score

    def _optimize_thresholds_by_regime(self, calibration_points: List[CalibrationPoint],
                                     percentile_analysis: Dict) -> Dict[str, float]:
        """
        Otimiza thresholds separadamente por regime de mercado

        Args:
            calibration_points: Lista de pontos de calibração
            percentile_analysis: Análise de percentis como fallback

        Returns:
            Dict com thresholds otimizados por regime
        """
        try:
            # Classificar pontos por regime
            regimes = self._classify_market_regimes(calibration_points)

            # Otimizar para cada regime
            regime_thresholds = {}
            for regime_name, regime_points in regimes.items():
                if len(regime_points) >= 10:  # Mínimo de pontos para otimização
                    logger.info(f"   Otimizando regime {regime_name}: {len(regime_points)} pontos")
                    regime_thresholds[regime_name] = self._optimize_thresholds_f1(
                        regime_points, percentile_analysis
                    )
                else:
                    logger.warning(f"   Poucos pontos para regime {regime_name}: {len(regime_points)}")

            # Se não conseguiu otimizar nenhum regime, usar otimização geral
            if not regime_thresholds:
                logger.warning("   Nenhum regime com pontos suficientes, usando otimização geral")
                return self._optimize_thresholds_f1(calibration_points, percentile_analysis)

            # Combinar thresholds dos regimes (média ponderada por número de pontos)
            combined_thresholds = self._combine_regime_thresholds(regime_thresholds, regimes)

            # Adicionar metadados de regime
            combined_thresholds['_regime_metadata'] = {
                'regimes_optimized': list(regime_thresholds.keys()),
                'regime_counts': {name: len(points) for name, points in regimes.items()}
            }

            return combined_thresholds

        except Exception as e:
            logger.error(f"Erro na otimização por regime: {e}")
            return self._optimize_thresholds_f1(calibration_points, percentile_analysis)

    def _classify_market_regimes(self, calibration_points: List[CalibrationPoint]) -> Dict[str, List[CalibrationPoint]]:
        """Classifica pontos por regime de mercado"""
        import numpy as np

        regimes = {'normal': [], 'pump': [], 'crash': []}

        # Calcular estatísticas para classificação
        returns = [point.future_return_4h for point in calibration_points]
        volumes = [point.volume_surge for point in calibration_points]

        return_p75 = np.percentile(returns, 75)
        return_p25 = np.percentile(returns, 25)
        volume_p75 = np.percentile(volumes, 75)

        for point in calibration_points:
            # Classificação baseada em retorno e volume
            if point.future_return_4h > return_p75 and point.volume_surge > volume_p75:
                regimes['pump'].append(point)
            elif point.future_return_4h < return_p25:
                regimes['crash'].append(point)
            else:
                regimes['normal'].append(point)

        return regimes

    def _combine_regime_thresholds(self, regime_thresholds: Dict[str, Dict],
                                 regimes: Dict[str, List]) -> Dict[str, float]:
        """Combina thresholds de diferentes regimes usando média ponderada"""
        import numpy as np

        combined = {}
        total_points = sum(len(points) for points in regimes.values())

        # Para cada métrica, calcular média ponderada
        all_metrics = set()
        for thresholds in regime_thresholds.values():
            all_metrics.update(thresholds.keys())

        for metric in all_metrics:
            if metric.startswith('_'):  # Pular metadados
                continue

            weighted_sum = 0
            total_weight = 0

            for regime_name, thresholds in regime_thresholds.items():
                if metric in thresholds:
                    weight = len(regimes[regime_name])
                    weighted_sum += thresholds[metric] * weight
                    total_weight += weight

            if total_weight > 0:
                combined[metric] = weighted_sum / total_weight

        return combined

    async def _fetch_historical_data(self, symbol: str, start_time: datetime, end_time: datetime) -> Optional[pd.DataFrame]:
        """
        Obtém dados históricos REAIS da API Binance

        Args:
            symbol: Símbolo do ativo
            start_time: Data/hora de início
            end_time: Data/hora de fim

        Returns:
            DataFrame com dados OHLCV históricos ou None se falhar
        """
        try:
            # Verificar se o trading system tem acesso à integração de mercado
            if not hasattr(self.trading_system, 'exchange') or not self.trading_system.exchange:
                logger.error(" Sistema de trading não tem acesso à exchange")
                return None

            # Usar timeframe de 1 hora para ter dados suficientes mas não excessivos
            timeframe = '1h'

            # Calcular limite de candles necessários
            hours_diff = int((end_time - start_time).total_seconds() / 3600)
            limit = min(hours_diff + 24, 5000)  # Aumentado para 5000 candles para mais dados históricos

            logger.debug(f" Buscando {limit} candles de {timeframe} para {symbol}")

            # Método 1: Tentar usar fetch_ohlcv com since
            since_timestamp = int(start_time.timestamp() * 1000)  # Converter para milliseconds

            try:
                ohlcv_data = self.trading_system.exchange.fetch_ohlcv(
                    symbol,
                    timeframe,
                    since=since_timestamp,
                    limit=limit
                )

                if ohlcv_data and len(ohlcv_data) > 0:
                    # Converter para DataFrame
                    df = pd.DataFrame(ohlcv_data, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
                    df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms', utc=True)
                    df.set_index('timestamp', inplace=True)

                    # Converter start_time e end_time para timezone-aware se necessário
                    start_time_tz = start_time.replace(tzinfo=timezone.utc) if start_time.tzinfo is None else start_time
                    end_time_tz = end_time.replace(tzinfo=timezone.utc) if end_time.tzinfo is None else end_time

                    # Filtrar pelo período desejado
                    df = df[(df.index >= start_time_tz) & (df.index <= end_time_tz)]

                    logger.debug(f"Método 1 sucesso: {len(df)} candles obtidos")
                    return df

            except Exception as e:
                logger.warning(f" Método 1 falhou para {symbol}: {e}")

            # Método 2: Tentar usar fetch_ohlcv sem since (dados mais recentes)
            try:
                ohlcv_data = self.trading_system.exchange.fetch_ohlcv(
                    symbol,
                    timeframe,
                    limit=limit
                )

                if ohlcv_data and len(ohlcv_data) > 0:
                    # Converter para DataFrame
                    df = pd.DataFrame(ohlcv_data, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
                    df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms', utc=True)
                    df.set_index('timestamp', inplace=True)

                    # Converter start_time para timezone-aware se necessário
                    start_time_tz = start_time.replace(tzinfo=timezone.utc) if start_time.tzinfo is None else start_time

                    # Verificar se temos dados suficientes no período
                    recent_data = df[df.index >= start_time_tz]
                    if len(recent_data) >= (hours_diff * 0.5):  # Pelo menos 50% dos dados esperados
                        logger.debug(f"Método 2 sucesso: {len(recent_data)} candles obtidos")
                        return recent_data

            except Exception as e:
                logger.warning(f" Método 2 falhou para {symbol}: {e}")

            # Método 3: Tentar usar método de dados históricos do sistema (se disponível)
            try:
                if hasattr(self.trading_system, 'market_integration') and self.trading_system.market_integration:
                    from qsi.qualia.market.base_integration import MarketSpec

                    spec = MarketSpec(symbol=symbol, timeframe=timeframe)
                    df = await self.trading_system.market_integration.fetch_historical_data(
                        spec=spec,
                        start_date=start_time,
                        end_date=end_time,
                        use_cache=False
                    )

                    if df is not None and not df.empty:
                        logger.debug(f" Método 3 sucesso: {len(df)} candles obtidos")
                        return df

            except Exception as e:
                logger.warning(f" Método 3 falhou para {symbol}: {e}")

            logger.error(f" Todos os métodos falharam para obter dados históricos de {symbol}")
            return None

        except Exception as e:
            logger.error(f" Erro geral obtendo dados históricos para {symbol}: {e}")
            return None
    
    def _calculate_percentiles(self, points: List[CalibrationPoint]) -> Dict[str, Dict[str, float]]:
        """Calcula percentis das métricas"""
        if not points:
            return {}
        
        metrics = {
            'consciousness': [p.consciousness for p in points],
            'coherence': [p.coherence for p in points],
            'confidence': [p.confidence for p in points],
            'volume_surge': [p.volume_surge for p in points],
            'momentum': [abs(p.momentum) for p in points]
        }
        
        percentile_analysis = {}
        for metric_name, values in metrics.items():
            if values:
                percentile_analysis[metric_name] = {
                    'p10': float(np.percentile(values, 10)),
                    'p15': float(np.percentile(values, 15)),
                    'p20': float(np.percentile(values, 20)),
                    'p25': float(np.percentile(values, 25)),
                    'p30': float(np.percentile(values, 30)),
                    'p50': float(np.percentile(values, 50)),  # Corrigido: p50 em vez de median
                    'p90': float(np.percentile(values, 90)),  # Adicionado: p90 necessário
                    'median': float(np.median(values)),
                    'mean': float(np.mean(values)),
                    'std': float(np.std(values))
                }
        
        return percentile_analysis
    
    def _calculate_aggregated_thresholds(self, results: Dict[str, CalibrationResults]) -> Dict[str, float]:
        """Calcula thresholds agregados de todos os ativos para TODAS as métricas"""
        if not results:
            return {}

        # Lista completa de métricas QUALIA
        all_metrics = [
            'consciousness', 'coherence', 'confidence',
            'volume_surge_min', 'momentum_min'
        ]

        # Coletar thresholds recomendados de todos os ativos
        aggregated_thresholds = {}
        for metric in all_metrics:
            values = []
            for result in results.values():
                if metric in result.recommended_thresholds:
                    values.append(result.recommended_thresholds[metric])

            if values:
                # Usar mediana para robustez contra outliers
                aggregated_thresholds[metric] = float(np.median(values))
                logger.debug(f" {metric}: {len(values)} valores, mediana = {aggregated_thresholds[metric]:.3f}")

        return aggregated_thresholds
    
    async def _save_calibration_results(self, results: Dict[str, CalibrationResults], 
                                      aggregated_thresholds: Dict[str, float]):
        """Salva resultados da calibração"""
        timestamp = datetime.now(timezone.utc).isoformat()
        
        calibration_report = {
            'timestamp': timestamp,
            'aggregated_thresholds': aggregated_thresholds,
            'individual_results': {
                symbol: {
                    'total_points': result.total_points,
                    'profitable_points': result.profitable_points,
                    'success_rate': result.success_rate,
                    'recommended_thresholds': result.recommended_thresholds,
                    'percentile_analysis': result.percentile_analysis
                }
                for symbol, result in results.items()
            }
        }
        
        # Salvar em arquivo
        calibration_dir = Path('data/calibration')
        calibration_dir.mkdir(parents=True, exist_ok=True)
        
        filename = f"geometric_metrics_calibration_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        filepath = calibration_dir / filename
        
        with open(filepath, 'w') as f:
            json.dump(calibration_report, f, indent=2, default=str)
        
        logger.info(f"Resultados salvos em: {filepath}")

        # Log resumo
        logger.info("THRESHOLDS RECOMENDADOS:")
        for metric, value in aggregated_thresholds.items():
            logger.info(f"   {metric}: {value:.3f}")

    async def apply_calibrated_thresholds(self, results: Dict[str, CalibrationResults],
                                        gradual_factor: float = 0.7):
        """
        Aplica TODOS os thresholds calibrados ao sistema de forma unificada

        Args:
            results: Resultados da calibração
            gradual_factor: Fator de aplicação gradual (0.7 = 70% novo, 30% antigo)
        """
        aggregated = self._calculate_aggregated_thresholds(results)

        if not aggregated:
            logger.warning("Nenhum threshold calibrado disponivel")
            return

        logger.info("Aplicando thresholds calibrados ao sistema...")

        # Atualizar TODOS os thresholds no sistema de trading
        updated_thresholds = {}

        for metric, new_value in aggregated.items():
            # Obter valor atual
            current_value = self.trading_system.quantum_thresholds.get(metric, new_value)

            # Aplicar mudança gradual para estabilidade
            final_value = (new_value * gradual_factor) + (current_value * (1 - gradual_factor))
            updated_thresholds[metric] = final_value

            logger.info(f"AJUSTE {metric}: {current_value:.3f} -> {final_value:.3f} "
                       f"(calibrado: {new_value:.3f})")

        # Atualizar sistema de trading com TODOS os thresholds
        self.trading_system.quantum_thresholds.update(updated_thresholds)

        # Atualizar adaptive_manager se disponível
        if hasattr(self.trading_system, 'adaptive_manager') and self.trading_system.adaptive_manager:
            # Atualizar thresholds no adaptive_manager
            current_thresholds = self.trading_system.adaptive_manager.current_thresholds

            # Mapear para campos do ThresholdConfig
            threshold_mapping = {
                'consciousness': 'consciousness',
                'coherence': 'coherence',
                'confidence': 'confidence',
                'volume_surge_min': 'volume_surge_min',
                'momentum_min': 'momentum_min'
            }

            for metric, field in threshold_mapping.items():
                if metric in updated_thresholds and hasattr(current_thresholds, field):
                    setattr(current_thresholds, field, updated_thresholds[metric])
                    logger.debug(f" Adaptive manager {field} atualizado: {updated_thresholds[metric]:.3f}")

        # Persistir thresholds no arquivo de configuração
        await self._persist_thresholds_to_config(updated_thresholds)

        logger.info("OK TODOS os thresholds aplicados ao sistema!")

    async def _persist_thresholds_to_config(self, updated_thresholds: Dict[str, float]):
        """
        Persiste os thresholds calibrados no arquivo qualia_config.yaml

        Args:
            updated_thresholds: Dicionário com os novos thresholds
        """
        try:
            import yaml
            from pathlib import Path

            # Caminho para o arquivo de configuração
            config_path = Path('config/qualia_config.yaml')

            if not config_path.exists():
                logger.error(f"Arquivo de configuracao nao encontrado: {config_path}")
                return

            # Ler configuração atual
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)

            # Backup do arquivo original
            backup_path = config_path.with_suffix('.yaml.backup')
            with open(backup_path, 'w', encoding='utf-8') as f:
                yaml.dump(config, f, default_flow_style=False, allow_unicode=True)

            logger.info(f"Backup criado: {backup_path}")

            # Atualizar thresholds na configuração
            if 'quantum_thresholds' not in config:
                config['quantum_thresholds'] = {}

            original_values = {}
            for metric, new_value in updated_thresholds.items():
                # Salvar valor original para log
                original_values[metric] = config['quantum_thresholds'].get(metric, 'N/A')

                # Atualizar valor
                config['quantum_thresholds'][metric] = round(float(new_value), 3)

                logger.info(f"CONFIG {metric}: {original_values[metric]} -> {new_value:.3f}")

            # Adicionar metadados da calibração
            from datetime import datetime
            config['quantum_thresholds']['_calibration_metadata'] = {
                'last_calibration': datetime.now().isoformat(),
                'calibration_method': 'historical_simulation',
                'calibrated_by': 'QualiaMetricsCalibrator'
            }

            # Salvar configuração atualizada
            with open(config_path, 'w', encoding='utf-8') as f:
                yaml.dump(config, f, default_flow_style=False, allow_unicode=True, indent=2)

            logger.info(f"Configuracao atualizada salva em: {config_path}")
            logger.info("PERSISTENCIA: Thresholds calibrados salvos no arquivo de configuracao!")

        except Exception as e:
            logger.error(f"Erro persistindo thresholds no arquivo de configuracao: {e}")
            logger.warning("AVISO: Thresholds aplicados apenas em memoria - serao perdidos ao reiniciar!")

    def get_calibration_summary(self, results: Dict[str, CalibrationResults]) -> str:
        """Gera resumo completo da calibração unificada"""
        if not results:
            return "Nenhum resultado de calibração disponível"

        total_points = sum(r.total_points for r in results.values())
        total_profitable = sum(r.profitable_points for r in results.values())
        avg_success_rate = np.mean([r.success_rate for r in results.values()])

        summary = f"""
RESUMO DA CALIBRACAO UNIFICADA QUALIA
{'='*60}
Ativos analisados: {len(results)}
Pontos totais: {total_points:,}
Pontos lucrativos: {total_profitable:,}
Taxa de sucesso media: {avg_success_rate:.1%}

THRESHOLDS RECOMENDADOS (TODAS AS METRICAS):
"""

        aggregated = self._calculate_aggregated_thresholds(results)

        # Organizar por categoria
        main_metrics = ['consciousness', 'coherence', 'confidence']
        volume_momentum = ['volume_surge_min', 'momentum_min']

        summary += "\n    METRICAS PRINCIPAIS:\n"
        for metric in main_metrics:
            if metric in aggregated:
                summary += f"      {metric}: {aggregated[metric]:.3f}\n"

        summary += "\n    VOLUME & MOMENTUM:\n"
        for metric in volume_momentum:
            if metric in aggregated:
                summary += f"      {metric}: {aggregated[metric]:.3f}\n"

        summary += "\n    METRICAS GEOMETRICAS:\n"
        for metric in geometric:
            if metric in aggregated:
                summary += f"      {metric}: {aggregated[metric]:.3f}\n"

        return summary

    def generate_detailed_report(self, results: Dict[str, CalibrationResults]) -> Dict:
        """Gera relatório detalhado para análise"""
        if not results:
            return {}

        # Estatísticas por ativo
        asset_stats = {}
        for symbol, result in results.items():
            asset_stats[symbol] = {
                'success_rate': result.success_rate,
                'total_points': result.total_points,
                'profitable_points': result.profitable_points,
                'thresholds': result.recommended_thresholds
            }

        # Estatísticas por métrica
        metric_stats = {}
        all_metrics = ['consciousness', 'coherence', 'confidence', 'volume_surge_min',
                      'momentum_min']

        for metric in all_metrics:
            values = []
            for result in results.values():
                if metric in result.recommended_thresholds:
                    values.append(result.recommended_thresholds[metric])

            if values:
                metric_stats[metric] = {
                    'count': len(values),
                    'min': float(np.min(values)),
                    'max': float(np.max(values)),
                    'median': float(np.median(values)),
                    'mean': float(np.mean(values)),
                    'std': float(np.std(values)),
                    'q25': float(np.percentile(values, 25)),
                    'q75': float(np.percentile(values, 75))
                }

        # Correlações entre métricas (se possível)
        correlations = self._calculate_metric_correlations(results)

        return {
            'asset_statistics': asset_stats,
            'metric_statistics': metric_stats,
            'correlations': correlations,
            'aggregated_thresholds': self._calculate_aggregated_thresholds(results),
            'total_assets': len(results),
            'total_points': sum(r.total_points for r in results.values()),
            'total_profitable': sum(r.profitable_points for r in results.values()),
            'average_success_rate': float(np.mean([r.success_rate for r in results.values()]))
        }

    def _calculate_metric_correlations(self, results: Dict[str, CalibrationResults]) -> Dict:
        """Calcula correlações entre métricas (análise exploratória)"""
        try:
            # Coletar dados para análise de correlação
            data = []
            for result in results.values():
                if result.recommended_thresholds:
                    data.append(result.recommended_thresholds)

            if len(data) < 3:  # Precisa de pelo menos 3 pontos para correlação
                return {}

            # Converter para DataFrame para análise
            import pandas as pd
            df = pd.DataFrame(data)

            # Calcular correlações
            correlations = {}
            for col1 in df.columns:
                correlations[col1] = {}
                for col2 in df.columns:
                    if col1 != col2:
                        corr = df[col1].corr(df[col2])
                        if not np.isnan(corr):
                            correlations[col1][col2] = float(corr)

            return correlations

        except Exception as e:
            logger.debug(f"Erro calculando correlações: {e}")
            return {}

    def _detect_market_regime(self, df: pd.DataFrame, window_hours: int = 72) -> pd.Series:
        """
        Detecta regime de mercado baseado em drawdown

        Args:
            df: DataFrame com dados de preço
            window_hours: Janela para calcular rolling max (default: 72h = 3 dias)

        Returns:
            Series com regimes: 'normal', 'crash', 'pump'
        """
        try:
            # Calcular rolling max para detectar drawdown
            rolling_max = df['close'].rolling(window=window_hours, min_periods=1).max()
            drawdown = (df['close'] / rolling_max) - 1

            # Detectar regimes baseado em drawdown
            regimes = pd.Series('normal', index=df.index)
            regimes[drawdown < -0.08] = 'crash'  # Drawdown > 8%
            regimes[drawdown > 0.05] = 'pump'    # Pump > 5%

            return regimes

        except Exception as e:
            logger.warning(f"Erro detectando regime de mercado: {e}")
            return pd.Series('normal', index=df.index)

    def _calculate_regime_thresholds(self, results: Dict[str, CalibrationResults],
                                   regime_aware: bool) -> Dict[str, Dict[str, float]]:
        """
        Calcula thresholds separados por regime de mercado

        Args:
            results: Resultados de calibração por ativo
            regime_aware: Se deve usar calibração regime-aware

        Returns:
            Dict com thresholds por regime: {'normal': {...}, 'crash': {...}, 'pump': {...}}
        """
        if not regime_aware:
            # Retornar thresholds globais
            global_thresholds = self._calculate_aggregated_thresholds(results)
            return {
                'normal': global_thresholds,
                'crash': global_thresholds,
                'pump': global_thresholds
            }

        try:
            regime_thresholds = {'normal': {}, 'crash': {}, 'pump': {}}

            # Coletar dados por regime (simulação - na implementação real seria baseado em dados históricos)
            all_metrics = ['consciousness', 'coherence', 'confidence', 'volume_surge_min', 'momentum_min']

            for metric in all_metrics:
                values_by_regime = {'normal': [], 'crash': [], 'pump': []}

                for result in results.values():
                    if metric in result.recommended_thresholds:
                        # Simular distribuição por regime (na implementação real seria baseado em dados reais)
                        base_value = result.recommended_thresholds[metric]
                        values_by_regime['normal'].append(base_value)
                        values_by_regime['crash'].append(base_value * 0.8)  # Mais conservador em crash
                        values_by_regime['pump'].append(base_value * 1.2)   # Mais agressivo em pump

                # Calcular medianas por regime
                for regime in ['normal', 'crash', 'pump']:
                    if values_by_regime[regime]:
                        regime_thresholds[regime][metric] = float(np.median(values_by_regime[regime]))

            logger.info("Thresholds regime-aware calculados")
            return regime_thresholds

        except Exception as e:
            logger.error(f"Erro calculando thresholds por regime: {e}")
            # Fallback para thresholds globais
            global_thresholds = self._calculate_aggregated_thresholds(results)
            return {
                'normal': global_thresholds,
                'crash': global_thresholds,
                'pump': global_thresholds
            }

    def _apply_temporal_weighting(self, short_results: Dict[str, CalibrationResults],
                                long_results: Dict[str, CalibrationResults],
                                w_short: float) -> Dict[str, CalibrationResults]:
        """
        Aplica peso temporal adaptativo combinando resultados de janelas curta e longa

        Args:
            short_results: Resultados da janela curta (sensibilidade)
            long_results: Resultados da janela longa (robustez)
            w_short: Peso da janela curta (0-1)

        Returns:
            Resultados combinados com peso temporal
        """
        try:
            combined_results = {}
            w_long = 1 - w_short

            for symbol in long_results.keys():
                if symbol in short_results:
                    short_res = short_results[symbol]
                    long_res = long_results[symbol]

                    # Combinar thresholds com peso temporal
                    combined_thresholds = {}
                    for metric in long_res.recommended_thresholds.keys():
                        if metric in short_res.recommended_thresholds:
                            short_val = short_res.recommended_thresholds[metric]
                            long_val = long_res.recommended_thresholds[metric]
                            combined_val = w_short * short_val + w_long * long_val
                            combined_thresholds[metric] = combined_val
                        else:
                            combined_thresholds[metric] = long_res.recommended_thresholds[metric]

                    # Criar resultado combinado
                    combined_results[symbol] = CalibrationResults(
                        symbol=symbol,
                        recommended_thresholds=combined_thresholds,
                        success_rate=(w_short * short_res.success_rate + w_long * long_res.success_rate),
                        total_points=long_res.total_points,
                        profitable_points=long_res.profitable_points,
                        avg_profit=long_res.avg_profit,
                        max_profit=max(short_res.max_profit, long_res.max_profit),
                        min_profit=min(short_res.min_profit, long_res.min_profit),
                        volatility=long_res.volatility
                    )
                else:
                    combined_results[symbol] = long_results[symbol]

            logger.info(f"Peso temporal aplicado: w_short={w_short:.3f}, w_long={w_long:.3f}")
            return combined_results

        except Exception as e:
            logger.error(f"Erro aplicando peso temporal: {e}")
            return long_results  # Fallback para resultados da janela longa

    def get_calibration_summary(self, results: Dict[str, CalibrationResults]) -> str:
        """
        Gera resumo textual dos resultados de calibração

        Args:
            results: Resultados de calibração por ativo

        Returns:
            String com resumo formatado
        """
        if not results:
            return "Nenhum resultado de calibração disponível"

        try:
            summary_lines = []
            summary_lines.append("\n" + "="*70)
            summary_lines.append(" RESUMO DA CALIBRAÇÃO UNIFICADA")
            summary_lines.append("="*70)

            # Estatísticas gerais
            total_assets = len(results)
            avg_success_rate = np.mean([r.success_rate for r in results.values()])
            total_points = sum([r.total_points for r in results.values()])
            total_profitable = sum([r.profitable_points for r in results.values()])

            summary_lines.append(f"Ativos calibrados: {total_assets}")
            summary_lines.append(f"Taxa de sucesso média: {avg_success_rate:.1%}")
            summary_lines.append(f"Total de pontos analisados: {total_points}")
            summary_lines.append(f"Pontos lucrativos: {total_profitable}")

            # Top 5 ativos por performance
            sorted_results = sorted(results.items(), key=lambda x: x[1].success_rate, reverse=True)
            summary_lines.append(f"\nTOP 5 ATIVOS POR PERFORMANCE:")
            for i, (symbol, result) in enumerate(sorted_results[:5], 1):
                summary_lines.append(f"  {i}. {symbol}: {result.success_rate:.1%} "
                                   f"({result.profitable_points}/{result.total_points} pontos)")

            # Thresholds agregados
            aggregated = self._calculate_aggregated_thresholds(results)
            summary_lines.append(f"\nTHRESHOLDS AGREGADOS RECOMENDADOS:")
            for metric, value in aggregated.items():
                summary_lines.append(f"  {metric}: {value:.3f}")

            return "\n".join(summary_lines)

        except Exception as e:
            logger.error(f"Erro gerando resumo: {e}")
            return f"Erro gerando resumo: {e}"

    def generate_detailed_report(self, results: Dict[str, CalibrationResults]) -> Dict:
        """
        Gera relatório detalhado em formato JSON

        Args:
            results: Resultados de calibração por ativo

        Returns:
            Dict com relatório detalhado
        """
        try:
            report = {
                'timestamp': datetime.now().isoformat(),
                'calibration_type': 'unified_historical_simulation',
                'total_assets': len(results),
                'summary_statistics': {},
                'asset_results': {},
                'aggregated_thresholds': {},
                'performance_metrics': {}
            }

            if results:
                # Estatísticas resumidas
                success_rates = [r.success_rate for r in results.values()]
                report['summary_statistics'] = {
                    'avg_success_rate': float(np.mean(success_rates)),
                    'median_success_rate': float(np.median(success_rates)),
                    'std_success_rate': float(np.std(success_rates)),
                    'min_success_rate': float(np.min(success_rates)),
                    'max_success_rate': float(np.max(success_rates))
                }

                # Resultados por ativo
                for symbol, result in results.items():
                    report['asset_results'][symbol] = {
                        'success_rate': result.success_rate,
                        'total_points': result.total_points,
                        'profitable_points': result.profitable_points,
                        'avg_profit': result.avg_profit,
                        'max_profit': result.max_profit,
                        'min_profit': result.min_profit,
                        'volatility': result.volatility,
                        'recommended_thresholds': result.recommended_thresholds
                    }

                # Thresholds agregados
                report['aggregated_thresholds'] = self._calculate_aggregated_thresholds(results)

                # Métricas de performance
                report['performance_metrics'] = self.generate_detailed_report_metrics(results)

            return report

        except Exception as e:
            logger.error(f"Erro gerando relatório detalhado: {e}")
            return {'error': str(e), 'timestamp': datetime.now().isoformat()}

    async def apply_calibrated_thresholds(self, results: Dict[str, CalibrationResults],
                                        gradual_factor: float = 0.7):
        """
        Aplica thresholds calibrados ao sistema de trading

        Args:
            results: Resultados de calibração
            gradual_factor: Fator de aplicação gradual (0.7 = 70% dos novos valores)
        """
        try:
            logger.info(f"Aplicando thresholds calibrados (fator gradual: {gradual_factor:.1%})...")

            # Calcular thresholds agregados
            new_thresholds = self._calculate_aggregated_thresholds(results)

            # Obter thresholds atuais
            current_config = self.config_manager.config
            current_thresholds = current_config.get('quantum_thresholds', {})

            # Aplicar fator gradual
            applied_thresholds = {}
            for metric, new_value in new_thresholds.items():
                if metric in current_thresholds:
                    current_value = current_thresholds[metric]
                    # Aplicação gradual: novo = atual + gradual_factor * (novo - atual)
                    applied_value = current_value + gradual_factor * (new_value - current_value)
                    applied_thresholds[metric] = applied_value
                    logger.info(f"  {metric}: {current_value:.3f} -> {applied_value:.3f} "
                              f"(target: {new_value:.3f})")
                else:
                    applied_thresholds[metric] = new_value
                    logger.info(f"  {metric}: NEW -> {new_value:.3f}")

            # Atualizar configuração
            await self._update_config_with_thresholds(applied_thresholds)

            # Atualizar trading modes se disponível
            if hasattr(self.trading_system, 'adaptive_manager') and self.trading_system.adaptive_manager:
                await self._update_trading_modes(applied_thresholds)

            logger.info("Thresholds aplicados com sucesso!")

        except Exception as e:
            logger.error(f"Erro aplicando thresholds: {e}")
            raise

    def generate_detailed_report_metrics(self, results: Dict[str, CalibrationResults]) -> Dict:
        """Gera métricas detalhadas para o relatório"""
        try:
            metrics = {}

            if results:
                # Distribuição de success rates
                success_rates = [r.success_rate for r in results.values()]
                metrics['success_rate_distribution'] = {
                    'q25': float(np.percentile(success_rates, 25)),
                    'q50': float(np.percentile(success_rates, 50)),
                    'q75': float(np.percentile(success_rates, 75)),
                    'q90': float(np.percentile(success_rates, 90))
                }

                # Análise de lucros
                avg_profits = [r.avg_profit for r in results.values() if r.avg_profit is not None]
                if avg_profits:
                    metrics['profit_analysis'] = {
                        'avg_profit_mean': float(np.mean(avg_profits)),
                        'avg_profit_std': float(np.std(avg_profits)),
                        'best_avg_profit': float(np.max(avg_profits)),
                        'worst_avg_profit': float(np.min(avg_profits))
                    }

                # Análise de volatilidade
                volatilities = [r.volatility for r in results.values() if r.volatility is not None]
                if volatilities:
                    metrics['volatility_analysis'] = {
                        'avg_volatility': float(np.mean(volatilities)),
                        'volatility_range': [float(np.min(volatilities)), float(np.max(volatilities))]
                    }

            return metrics

        except Exception as e:
            logger.warning(f"Erro gerando métricas detalhadas: {e}")
            return {}

    async def _update_config_with_thresholds(self, thresholds: Dict[str, float]):
        """Atualiza arquivo de configuração com novos thresholds"""
        try:
            import yaml

            # Obter caminho do arquivo de configuração
            config_path = Path('config/qualia_config.yaml')

            # Carregar configuração atual
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)

            # Atualizar thresholds quânticos
            if 'quantum_thresholds' not in config:
                config['quantum_thresholds'] = {}

            # Separar min_combined_score dos outros thresholds e converter para float Python
            quantum_thresholds = {k: float(v) for k, v in thresholds.items() if k != 'min_combined_score'}
            config['quantum_thresholds'].update(quantum_thresholds)

            # Atualizar min_combined_score na seção signal_quality_filters
            if 'min_combined_score' in thresholds:
                if 'signal_quality_filters' not in config:
                    config['signal_quality_filters'] = {}
                config['signal_quality_filters']['min_combined_score'] = float(thresholds['min_combined_score'])

            # Adicionar metadados da calibração
            config['quantum_thresholds']['_calibration_metadata'] = {
                'last_calibration': datetime.now().isoformat(),
                'calibration_method': 'historical_simulation_enhanced',
                'calibrated_by': 'QualiaMetricsCalibrator',
                'gradual_application': True
            }

            # Salvar configuração atualizada
            with open(config_path, 'w', encoding='utf-8') as f:
                yaml.dump(config, f, default_flow_style=False, allow_unicode=True, indent=2)

            logger.info(f"Configuração atualizada salva em: {config_path}")

        except Exception as e:
            logger.error(f"Erro atualizando configuração: {e}")
            raise

    async def _update_trading_modes(self, thresholds: Dict[str, float]):
        """Atualiza trading modes com novos thresholds"""
        try:
            adaptive_manager = self.trading_system.adaptive_manager

            # Atualizar cada modo de trading
            for mode_name, config in adaptive_manager.threshold_configs.items():
                # Aplicar thresholds com fatores específicos por modo
                mode_factor = self._get_mode_factor(mode_name)

                for metric, base_value in thresholds.items():
                    adjusted_value = base_value * mode_factor
                    setattr(config, metric, adjusted_value)

            logger.info("Trading modes atualizados com novos thresholds")

        except Exception as e:
            logger.warning(f"Erro atualizando trading modes: {e}")

    def _get_mode_factor(self, mode_name: str) -> float:
        """Retorna fator de ajuste por modo de trading"""
        mode_factors = {
            'conservative': 1.2,  # 20% mais conservador
            'moderate': 1.0,      # Valores base
            'aggressive': 0.8     # 20% mais agressivo
        }
        return mode_factors.get(mode_name.lower(), 1.0)
