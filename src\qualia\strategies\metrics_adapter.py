"""
QUALIA Metrics Adapter

Este módulo fornece adaptadores para métricas quantitativas que podem ser
usadas por estratégias de trading no sistema QUALIA.
"""

from ..utils.logger import get_logger
import numpy as np
import pandas as pd
from typing import Dict, Any, List, Optional, Tuple, Union

logger = get_logger(__name__)


class QuantumMetricsAdapter:
    """Adaptador para métricas quânticas.

    Esta classe atua como interface entre estratégias de trading e o módulo
    de cálculo de métricas quânticas do QUALIA. Sempre que um método precisa
    de ruído pseudoaleatório, a semente do gerador é derivada de
    ``hash(tuple(prices[-50:]))``. Essa abordagem mantém a execução
    determinística para a mesma série de preços e evita efeitos colaterais
    sobre o estado global do ``numpy.random``.
    """

    def __init__(self, qubits: int = 8, quantum_weight: float = 0.1) -> None:
        """Inicializa o adaptador de métricas quânticas.

        Parameters
        ----------
        qubits : int, optional
            Número de qubits para o cálculo quântico.
        quantum_weight : float, optional
            Peso do cálculo quântico (entre ``0`` e ``1``).
        """
        self.qubits = qubits
        self.quantum_weight = min(max(quantum_weight, 0.0), 1.0)
        logger.info(f"QuantumMetricsAdapter inicializado com {qubits} qubits")

    def calculate_quantum_momentum(
        self,
        prices: np.ndarray,
        volumes: Optional[np.ndarray] = None,
        volatility: Optional[float] = None,
    ) -> float:
        """Calcula o momentum quântico de uma série de preços.

        YAA CORREÇÃO CRÍTICA: Agora usa o método unificado para garantir
        consistência entre calibração e produção, eliminando discrepâncias
        de magnitude.

        Args:
            prices: Array de preços
            volumes: Array de volumes (opcional)
            volatility: Volatilidade do ativo (opcional)

        Returns:
            Valor do momentum quântico entre -1 e 1
        """
        if len(prices) < 2:
            return 0.0

        # YAA CORREÇÃO: Usar método unificado em vez da implementação antiga
        # que causava discrepâncias de magnitude entre calibração e produção
        from ..core.momentum_unified import calculate_unified_momentum

        # YAA CORREÇÃO FINAL: Usar método unificado com peso quântico específico
        from ..core.momentum_unified import UnifiedMomentumCalculator

        # Criar calculadora com o mesmo peso quântico do adapter
        calculator = UnifiedMomentumCalculator(quantum_weight=self.quantum_weight)

        # Calcular momentum usando o método unificado com componente quântico
        momentum = calculator.calculate_unified_momentum(
            prices=prices,
            volumes=volumes,
            add_quantum_noise=True
        )

        return float(momentum)

    def calculate_quantum_divergence(
        self, prices: np.ndarray, oscillator: np.ndarray, threshold: float = 0.7
    ) -> Tuple[bool, float]:
        """Detecta divergências quânticas entre preço e osciladores.

        O ruído pseudoaleatório é produzido por um RNG inicializado com
        ``hash(tuple(prices[-50:]))``. Dessa forma o cálculo é
        determinístico para a mesma série de preços e o estado global do
        RNG permanece intacto.

        Args:
            prices: Array de preços
            oscillator: Array de valores de oscilador (ex: RSI)
            threshold: Limiar para detecção de divergência

        Returns:
            Tupla (divergência detectada, força da divergência)
        """
        if len(prices) < 10 or len(oscillator) < 10:
            return False, 0.0

        # Implementação simplificada de detecção de divergência
        # (abordagem de substituição para o módulo completo)
        n_periods = min(5, len(prices) // 2)

        # Verificar se preço está subindo enquanto oscilador está caindo
        price_up = prices[-1] > prices[-n_periods]
        osc_down = oscillator[-1] < oscillator[-n_periods]
        bearish_div = price_up and osc_down

        # Verificar se preço está caindo enquanto oscilador está subindo
        price_down = prices[-1] < prices[-n_periods]
        osc_up = oscillator[-1] > oscillator[-n_periods]
        bullish_div = price_down and osc_up

        # Calcular magnitude baseada na diferença
        price_change = (prices[-1] / prices[-n_periods]) - 1
        osc_change = (oscillator[-1] / max(0.01, oscillator[-n_periods])) - 1

        # Divergência quântica (simplificada)
        divergence_strength = abs(price_change - osc_change) * self.quantum_weight

        # Adicionar um pouco de "incerteza quântica" sem alterar o RNG global
        seed = hash(tuple(prices[-50:])) & 0xFFFFFFFF
        rng = np.random.default_rng(seed)
        quantum_factor = 1.0 + rng.normal(0, 0.1) * self.quantum_weight
        divergence_strength *= quantum_factor

        has_divergence = (bearish_div or bullish_div) and (
            divergence_strength > threshold
        )

        return has_divergence, divergence_strength

    def get_quantum_market_regime(
        self, prices: np.ndarray, volumes: Optional[np.ndarray] = None
    ) -> Dict[str, float]:
        """
        Determina o regime de mercado atual usando análise quântica.

        Args:
            prices: Array de preços
            volumes: Array de volumes (opcional)

        Returns:
            Dicionário com probabilidades para cada regime
        """
        if len(prices) < 30:
            return {
                "trending_up": 0.25,
                "trending_down": 0.25,
                "ranging": 0.25,
                "volatile": 0.25,
            }

        # Análise clássica de regime de mercado
        returns = np.diff(prices) / prices[:-1]
        volatility = np.std(returns) * np.sqrt(252)

        # Trend strength
        price_sma20 = np.mean(prices[-20:])
        price_sma50 = np.mean(prices[-50:]) if len(prices) >= 50 else price_sma20
        trend_strength = (price_sma20 / price_sma50) - 1

        # Calcular probabilidades
        # Esta é uma versão simplificada do algoritmo original
        p_trending_up = max(0, min(1, 0.5 + trend_strength * 5))
        p_trending_down = max(0, min(1, 0.5 - trend_strength * 5))
        p_ranging = max(0, min(1, 1.0 - volatility * 10))
        p_volatile = max(0, min(1, volatility * 10))

        # Normalizar para que a soma seja 1
        total = p_trending_up + p_trending_down + p_ranging + p_volatile
        if total > 0:
            p_trending_up /= total
            p_trending_down /= total
            p_ranging /= total
            p_volatile /= total
        else:
            p_trending_up = p_trending_down = p_ranging = p_volatile = 0.25

        return {
            "trending_up": float(p_trending_up),
            "trending_down": float(p_trending_down),
            "ranging": float(p_ranging),
            "volatile": float(p_volatile),
        }
