#!/usr/bin/env python3
"""
QUALIA Adaptive Threshold System
Sistema inteligente de thresholds adaptativos baseado em performance real

FUNCIONALIDADES:
- Análise diagnóstica completa de métricas vs thresholds
- Thresholds adaptativos que se ajustam automaticamente
- Múltiplos modos: conservador/moderado/agressivo
- Logs detalhados para debugging
- Validação empírica baseada em resultados históricos
"""

import asyncio
import ccxt
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
import json
import logging
import os
from dataclasses import dataclass, asdict
from typing import Dict, List, Optional, Tuple
from enum import Enum

# Importar o sistema de configuração centralizado
from .config_manager import get_config_manager, ConfigurationError
from .intelligent_adaptation_system import (
    IntelligentAdaptationSystem,
    AdaptationState,
)

# Configurar logging detalhado
# Garantir que o diretório de logs existe
log_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'logs')
os.makedirs(log_dir, exist_ok=True)

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join(log_dir, f'qualia_adaptive_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class TradingMode(Enum):
    CONSERVATIVE = "conservative"
    MODERATE = "moderate"
    AGGRESSIVE = "aggressive"

@dataclass
class ThresholdConfig:
    """Configuração de thresholds por modo"""
    consciousness: float
    coherence: float
    confidence: float
    volume_surge_min: float
    momentum_min: float

@dataclass
class MetricAnalysis:
    """Análise de uma métrica específica"""
    symbol: str
    consciousness: float
    coherence: float
    confidence: float
    volume_surge: float
    momentum: float
    threshold_passed: bool
    failed_thresholds: List[str]
    quality_score: float
    combined_score: float  # Score combinado usado como critério final

class AdaptiveThresholdManager:
    """Gerenciador inteligente de thresholds adaptativos"""
    
    def __init__(self, config_path: str = 'config/qualia_config.yaml', trading_system=None):
        # CARREGAR CONFIGURAÇÕES DE THRESHOLD DO YAML (elimina hardcoding)
        self.trading_system = trading_system  # Referência para sincronização
        try:
            self.config_manager = get_config_manager(config_path)

            # Carregar configurações por modo do YAML
            trading_modes = self.config_manager.get('quantum_thresholds.trading_modes')

            self.threshold_configs = {
                TradingMode.CONSERVATIVE: ThresholdConfig(
                    consciousness=trading_modes['conservative']['consciousness'],
                    coherence=trading_modes['conservative']['coherence'],
                    confidence=trading_modes['conservative']['confidence'],
                    volume_surge_min=trading_modes['conservative']['volume_surge_min'],
                    momentum_min=trading_modes['conservative']['momentum_min']
                ),
                TradingMode.MODERATE: ThresholdConfig(
                    consciousness=trading_modes['moderate']['consciousness'],
                    coherence=trading_modes['moderate']['coherence'],
                    confidence=trading_modes['moderate']['confidence'],
                    volume_surge_min=trading_modes['moderate']['volume_surge_min'],
                    momentum_min=trading_modes['moderate']['momentum_min']
                ),
                TradingMode.AGGRESSIVE: ThresholdConfig(
                    consciousness=trading_modes['aggressive']['consciousness'],
                    coherence=trading_modes['aggressive']['coherence'],
                    confidence=trading_modes['aggressive']['confidence'],
                    volume_surge_min=trading_modes['aggressive']['volume_surge_min'],
                    momentum_min=trading_modes['aggressive']['momentum_min']
                )
            }

            logger.info("[OK] Configurações de threshold carregadas do YAML (sem hardcoding)")

        except ConfigurationError as e:
            logger.error(f"[ERROR] FALHA CRÍTICA: Não foi possível carregar configurações de threshold: {e}")
            raise SystemExit(f"Sistema adaptativo não pode inicializar sem configuração válida: {e}")
        
        # THRESHOLDS CALIBRADOS CIENTIFICAMENTE (baseado em 2,282 pontos históricos, 24.4% win rate)
        # Usar valores calibrados da raiz do YAML em vez de valores hardcoded
        base_consciousness = self.config_manager.get('quantum_thresholds.consciousness')
        base_coherence = self.config_manager.get('quantum_thresholds.coherence')
        base_confidence = self.config_manager.get('quantum_thresholds.confidence')
        base_volume_surge = self.config_manager.get('quantum_thresholds.volume_surge_min')
        base_momentum = self.config_manager.get('quantum_thresholds.momentum_min')

        self.empirical_thresholds = ThresholdConfig(
            consciousness=base_consciousness,
            coherence=base_coherence,
            confidence=base_confidence,
            volume_surge_min=base_volume_surge,
            momentum_min=base_momentum
        )

        logger.info(f"[CALIBRATED] Usando thresholds calibrados: C={base_consciousness:.3f}, Coh={base_coherence:.3f}, Conf={base_confidence:.3f}")

        # THRESHOLDS ULTRA-OTIMIZADOS (baseados nos valores calibrados - 85% dos valores)
        self.ultra_optimized_thresholds = ThresholdConfig(
            consciousness=base_consciousness * 0.85,
            coherence=base_coherence * 0.85,
            confidence=base_confidence * 0.85,
            volume_surge_min=base_volume_surge * 0.85,
            momentum_min=base_momentum * 0.85
        )
        
        # Estado atual
        self.current_mode = TradingMode.MODERATE
        self.current_thresholds = self.threshold_configs[self.current_mode]
        self.cycles_without_signals = 0
        self.total_assets_analyzed = 0
        self.metric_statistics = {
            'consciousness': [], 'coherence': [], 'confidence': [],
            'volume_surge': [], 'momentum': []
        }
        
        # Histórico de adaptações
        self.adaptation_history = []

        # SISTEMA DE ADAPTAÇÃO INTELIGENTE
        # Preparar thresholds calibrados para o sistema inteligente
        calibrated_thresholds = {
            'consciousness': base_consciousness,
            'coherence': base_coherence,
            'confidence': base_confidence,
            'volume_surge_min': base_volume_surge,
            'momentum_min': base_momentum
        }

        # Inicializar sistema de adaptação inteligente
        self.intelligent_adaptation = IntelligentAdaptationSystem(
            config_manager=self.config_manager,
            calibrated_thresholds=calibrated_thresholds,
            trading_system=self.trading_system  # Passar referência para aplicação direta
        )

        # Usar thresholds do sistema inteligente se habilitado
        if self.intelligent_adaptation.config['enabled']:
            logger.info("[INTELLIGENT] Sistema de Adaptação Inteligente ATIVADO")
            logger.info("[INTELLIGENT] Thresholds calibrados como base de referência")
            # Atualizar current_thresholds com os valores do sistema inteligente
            intelligent_thresholds = self.intelligent_adaptation.current_thresholds
            self.current_thresholds = ThresholdConfig(
                consciousness=intelligent_thresholds['consciousness'],
                coherence=intelligent_thresholds['coherence'],
                confidence=intelligent_thresholds['confidence'],
                volume_surge_min=intelligent_thresholds['volume_surge_min'],
                momentum_min=intelligent_thresholds['momentum_min']
            )
        else:
            logger.info("[INTELLIGENT] Sistema de Adaptação Inteligente DESABILITADO - usando thresholds fixos")
        self.performance_tracking = {
            'signals_generated': 0,
            'trades_executed': 0,
            'win_rate': 0.0,
            'last_adaptation': None
        }

        # Flag para garantir que a adaptação ocorra apenas uma vez por ciclo
        self._adaptation_executed_this_cycle = False

    def update_thresholds_from_memory(self, aggregated: Dict[str, float]):
        """Suaviza thresholds a partir das métricas agregadas do histórico."""
        if not aggregated:
            return

        for key in ['consciousness', 'coherence', 'confidence']:
            if key in aggregated:
                current = getattr(self.current_thresholds, key)
                new_value = current * 0.75 + aggregated[key] * 0.25
                setattr(self.current_thresholds, key, new_value)
    
    def analyze_metric_vs_threshold(self, metrics: Dict, symbol: str) -> MetricAnalysis:
        """Análise detalhada de métricas vs thresholds"""
        
        failed_thresholds = []
        
        # Verificar cada threshold
        if metrics['consciousness'] < self.current_thresholds.consciousness:
            failed_thresholds.append(f"consciousness ({metrics['consciousness']:.3f} < {self.current_thresholds.consciousness:.3f})")
        
        if metrics['coherence'] < self.current_thresholds.coherence:
            failed_thresholds.append(f"coherence ({metrics['coherence']:.3f} < {self.current_thresholds.coherence:.3f})")
        
        if metrics['confidence'] < self.current_thresholds.confidence:
            failed_thresholds.append(f"confidence ({metrics['confidence']:.3f} < {self.current_thresholds.confidence:.3f})")
        
        volume_surge_val = metrics.get('volume_surge', 1.0)
        if volume_surge_val < self.current_thresholds.volume_surge_min:
            failed_thresholds.append(
                f"volume_surge ({volume_surge_val:.3f} < {self.current_thresholds.volume_surge_min:.3f})"
            )
        
        if abs(metrics['momentum']) < self.current_thresholds.momentum_min:
            failed_thresholds.append(f"momentum ({abs(metrics['momentum']):.4f} < {self.current_thresholds.momentum_min:.4f})")

        threshold_passed = len(failed_thresholds) == 0
        quality_score = (metrics['consciousness'] + metrics['coherence'] + metrics['confidence']) / 3

        # Calcular combined_score usando a mesma lógica do sistema de trading
        combined_score = self._calculate_combined_score(metrics)

        return MetricAnalysis(
            symbol=symbol,
            consciousness=metrics['consciousness'],
            coherence=metrics['coherence'],
            confidence=metrics['confidence'],
            volume_surge=metrics.get('volume_surge', 1.0),
            momentum=metrics['momentum'],
            threshold_passed=threshold_passed,
            failed_thresholds=failed_thresholds,
            quality_score=quality_score,
            combined_score=combined_score
        )

    def _calculate_combined_score(self, metrics: Dict) -> float:
        """
        Calcula o combined_score usando a mesma lógica do sistema de trading

        Args:
            metrics: Dicionário com as métricas

        Returns:
            Score combinado (0.0 a 1.0)
        """
        # Calcular scores individuais
        consciousness_score = self._linear_gate(metrics['consciousness'], self.current_thresholds.consciousness)
        coherence_score = self._linear_gate(metrics['coherence'], self.current_thresholds.coherence)
        confidence_score = self._linear_gate(metrics['confidence'], self.current_thresholds.confidence)
        volume_score = self._sigmoid_gate(
            metrics.get('volume_surge', 1.0),
            self.current_thresholds.volume_surge_min,
            steepness=5.0,
        )
        momentum_score = self._sigmoid_gate(abs(metrics['momentum']), self.current_thresholds.momentum_min, steepness=3.0)

        # Pesos otimizados (mesmos do sistema de trading)
        weights = {
            'consciousness': 0.30,
            'coherence': 0.25,
            'confidence': 0.25,
            'volume': 0.10,
            'momentum': 0.10
        }

        combined_score = (
            weights['consciousness'] * consciousness_score +
            weights['coherence'] * coherence_score +
            weights['confidence'] * confidence_score +
            weights['volume'] * volume_score +
            weights['momentum'] * momentum_score
        )

        return combined_score

    def _linear_gate(self, value: float, threshold: float) -> float:
        """
        Gate linear para métricas já normalizadas (consciousness, coherence, confidence)

        Args:
            value: Valor da métrica (0.0 a 1.0)
            threshold: Threshold mínimo

        Returns:
            Score linear entre 0.0 e 1.0
        """
        if value >= threshold:
            # Acima do threshold: score linear de 0.5 a 1.0
            excess = value - threshold
            max_excess = 1.0 - threshold
            if max_excess > 0:
                return 0.5 + 0.5 * (excess / max_excess)
            else:
                return 1.0
        else:
            # Abaixo do threshold: score linear de 0.0 a 0.5
            return 0.5 * (value / threshold) if threshold > 0 else 0.0

    def _sigmoid_gate(self, value: float, threshold: float, steepness: float = 5.0) -> float:
        """
        Gate sigmoid para métricas não normalizadas (volume, momentum)

        Args:
            value: Valor da métrica
            threshold: Threshold mínimo
            steepness: Controla a inclinação da curva sigmoid

        Returns:
            Score sigmoid entre 0.0 e 1.0
        """
        import math

        if threshold <= 0:
            return 1.0 if value > 0 else 0.0

        # Normalizar valor pelo threshold
        normalized = value / threshold

        # Aplicar função sigmoid centrada em 1.0 (threshold)
        sigmoid_score = 1.0 / (1.0 + math.exp(-steepness * (normalized - 1.0)))

        return sigmoid_score

    def update_statistics(self, metrics: Dict):
        """Atualiza estatísticas das métricas com validação"""
        self.metric_statistics['consciousness'].append(metrics['consciousness'])
        self.metric_statistics['coherence'].append(metrics['coherence'])
        self.metric_statistics['confidence'].append(metrics['confidence'])

        # Adicionar volume_surge e momentum com validação
        if 'volume_surge' in metrics and metrics['volume_surge'] is not None:
            self.metric_statistics['volume_surge'].append(metrics['volume_surge'])
        if 'momentum' in metrics and metrics['momentum'] is not None:
            self.metric_statistics['momentum'].append(abs(metrics['momentum']))  # Usar valor absoluto

        self.total_assets_analyzed += 1

        # Manter apenas últimas 100 métricas para performance
        for metric_list in self.metric_statistics.values():
            if len(metric_list) > 100:
                metric_list.pop(0)
    
    def get_diagnostic_report(self) -> Dict:
        """Gera relatório diagnóstico completo"""
        if not self.metric_statistics['consciousness']:
            return {"error": "Nenhuma métrica coletada ainda"}
        
        stats = {}
        for metric, values in self.metric_statistics.items():
            if values:
                stats[metric] = {
                    'mean': np.mean(values),
                    'median': np.median(values),
                    'std': np.std(values),
                    'min': np.min(values),
                    'max': np.max(values),
                    'percentile_75': np.percentile(values, 75),
                    'percentile_90': np.percentile(values, 90),
                    'current_threshold': getattr(self.current_thresholds, metric, 'N/A')
                }
        
        # Análise de lacunas
        gaps = {}
        for metric in ['consciousness', 'coherence', 'confidence']:
            current_threshold = getattr(self.current_thresholds, metric)
            mean_value = stats[metric]['mean']
            gaps[metric] = {
                'gap': current_threshold - mean_value,
                'percentage_above_threshold': sum(1 for v in self.metric_statistics[metric] if v >= current_threshold) / len(self.metric_statistics[metric]) * 100
            }
        
        return {
            'current_mode': self.current_mode.value,
            'current_thresholds': asdict(self.current_thresholds),
            'cycles_without_signals': self.cycles_without_signals,
            'total_assets_analyzed': self.total_assets_analyzed,
            'metric_statistics': stats,
            'threshold_gaps': gaps,
            'adaptation_history': self.adaptation_history[-5:],  # Últimas 5 adaptações
            'performance': self.performance_tracking
        }
    
    def suggest_threshold_adjustments(self) -> Dict:
        """Sugere ajustes de threshold baseado em dados reais"""
        if not self.metric_statistics['consciousness']:
            return {"suggestion": "Aguardar mais dados"}
        
        suggestions = {}
        
        for metric in ['consciousness', 'coherence', 'confidence']:
            values = self.metric_statistics[metric]
            current_threshold = getattr(self.current_thresholds, metric)
            
            # Calcular percentis
            p75 = np.percentile(values, 75)
            p90 = np.percentile(values, 90)
            mean_val = np.mean(values)
            
            # Sugestões baseadas em distribuição
            if current_threshold > p90:
                suggestions[metric] = {
                    'current': current_threshold,
                    'suggested': p75,
                    'reason': f'Threshold muito alto - apenas {sum(1 for v in values if v >= current_threshold)/len(values)*100:.1f}% dos ativos passam',
                    'empirical_validated': getattr(self.empirical_thresholds, metric)
                }
            elif current_threshold > p75:
                suggestions[metric] = {
                    'current': current_threshold,
                    'suggested': (p75 + mean_val) / 2,
                    'reason': f'Threshold alto - {sum(1 for v in values if v >= current_threshold)/len(values)*100:.1f}% dos ativos passam',
                    'empirical_validated': getattr(self.empirical_thresholds, metric)
                }
        
        return suggestions
    
    def should_adapt_thresholds(self) -> bool:
        """Decide se deve adaptar thresholds com lógica otimizada"""
        # Adaptar se muitos ciclos sem sinais (reduzido de 5 para 3)
        if self.cycles_without_signals >= 3:
            return True

        # Adaptar baseado na taxa de aprovação atual
        if self.total_assets_analyzed >= 10:  # Reduzido de 20 para 10 (mais responsivo)
            current_pass_rate = self.calculate_current_pass_rate()

            # Meta: 10-20% de aprovação
            if current_pass_rate < 0.08:  # Menos de 8% - muito baixo
                logger.info(f"Taxa de aprovação muito baixa: {current_pass_rate:.1%} (meta: 10-20%)")
                return True
            elif current_pass_rate > 0.25:  # Mais de 25% - muito alto
                logger.info(f"Taxa de aprovação muito alta: {current_pass_rate:.1%} (meta: 10-20%)")
                return True

        return False

    def _is_emergency_situation(self, current_pass_rate: float) -> bool:
        """Detecta situação de emergência que requer ação contextual imediata"""
        # Emergência: 0% aprovação + muitos ciclos sem sinais
        if current_pass_rate == 0.0 and self.cycles_without_signals >= 3:
            return True

        # Emergência: Taxa extremamente baixa por muito tempo
        if current_pass_rate < 0.05 and self.cycles_without_signals >= 5:
            return True

        return False

    def _should_use_auto_tuning_for_large_deviations(self, current_pass_rate: float) -> bool:
        """Decide se deve usar auto-tuning para desvios grandes"""
        # Prevenir loop infinito - verificar se já executou auto-tuning neste ciclo
        if hasattr(self, '_auto_tuning_executed_this_cycle') and self._auto_tuning_executed_this_cycle:
            logger.info("Auto-tuning já executado neste ciclo - evitando loop infinito")
            return False

        # CORREÇÃO CRÍTICA: Verificar se Sistema Inteligente está em controle
        if hasattr(self, '_intelligent_system_in_control') and self._intelligent_system_in_control:
            logger.info("Sistema Inteligente em controle - auto-tuning bloqueado")
            return False

        # Precisa de dados suficientes para auto-tuning ser confiável
        if self.total_assets_analyzed < 20:
            logger.info(f"Poucos dados ({self.total_assets_analyzed}) - usando Sistema Inteligente")
            return False

        # CORREÇÃO CRÍTICA: Verificar emergência PRIMEIRO (prioridade absoluta)
        if self._is_emergency_situation(current_pass_rate):
            logger.info("EMERGÊNCIA DETECTADA - Auto-tuning BLOQUEADO")
            logger.info(f"   Taxa: {current_pass_rate:.1%}, Ciclos: {self.cycles_without_signals}")
            logger.info("   Sistema Inteligente tem PRIORIDADE ABSOLUTA em emergências")
            return False

        # Verificar se o sistema inteligente já está em modo emergência
        if (hasattr(self, 'intelligent_adaptation') and
            hasattr(self.intelligent_adaptation, 'current_state') and
            self.intelligent_adaptation.current_state.name == 'EMERGENCY'):
            logger.info("Sistema Inteligente em modo emergência - auto-tuning bloqueado")
            return False

        # CORREÇÃO: Verificar se taxa está adequada ANTES de considerar desvio
        if 0.10 <= current_pass_rate <= 0.20:
            logger.info(f"   Taxa adequada: {current_pass_rate:.1%} dentro da meta 10-20%")
            logger.info("   Auto-tuning desnecessário - taxa já no alvo")
            return False

        if current_pass_rate < 0.08 or current_pass_rate > 0.25:
            logger.info(f"   Desvio grande: {current_pass_rate:.1%} vs meta 10-20%")
            return True

        return False

    def should_use_auto_tuning(self) -> bool:
        """Método legado - mantido para compatibilidade"""
        current_pass_rate = self.calculate_current_pass_rate()
        return self._should_use_auto_tuning_for_large_deviations(current_pass_rate)

    def calculate_current_pass_rate(self) -> float:
        """Calcula taxa de aprovação atual baseada em métricas coletadas"""
        if not self.metric_statistics['consciousness']:
            return 0.0

        total_assets = len(self.metric_statistics['consciousness'])
        passed_assets = 0

        for i in range(total_assets):
            # Verificar se ativo passou em todos os thresholds
            consciousness_pass = self.metric_statistics['consciousness'][i] >= self.current_thresholds.consciousness
            coherence_pass = self.metric_statistics['coherence'][i] >= self.current_thresholds.coherence
            confidence_pass = self.metric_statistics['confidence'][i] >= self.current_thresholds.confidence

            # Verificar volume_surge e momentum se disponíveis
            volume_pass = True
            momentum_pass = True

            if 'volume_surge' in self.metric_statistics and i < len(self.metric_statistics['volume_surge']):
                volume_pass = self.metric_statistics['volume_surge'][i] >= self.current_thresholds.volume_surge_min

            if 'momentum' in self.metric_statistics and i < len(self.metric_statistics['momentum']):
                momentum_pass = abs(self.metric_statistics['momentum'][i]) >= self.current_thresholds.momentum_min

            if consciousness_pass and coherence_pass and confidence_pass and volume_pass and momentum_pass:
                passed_assets += 1

        return passed_assets / total_assets if total_assets > 0 else 0.0
    
    def adapt_thresholds(self, force_mode: Optional[TradingMode] = None):
        """Adapta thresholds com lógica revisada baseada em situação específica"""

        if self._adaptation_executed_this_cycle:
            logger.info("Adaptação já executada neste ciclo - ignorando nova chamada")
            return

        # Marcar execução para evitar chamadas repetidas
        self._adaptation_executed_this_cycle = True

        # CORREÇÃO: Usar taxa mais recente se disponível (do último log_detailed_analysis)
        current_pass_rate = getattr(self, '_last_detailed_pass_rate', None)
        if current_pass_rate is None:
            current_pass_rate = self.calculate_current_pass_rate()
            logger.info(f" Usando taxa histórica: {current_pass_rate:.1f}%")
        else:
            logger.info(f" Usando taxa do último resumo: {current_pass_rate:.1f}%")

        # PRIORIDADE 1: Sistema Inteligente para EMERGÊNCIAS
        if self._is_emergency_situation(current_pass_rate):
            logger.info(" EMERGÊNCIA DETECTADA - Usando Sistema Inteligente")
            logger.info(f"   Taxa: {current_pass_rate:.1%}, Ciclos sem sinais: {self.cycles_without_signals}")
            result = self._adapt_with_intelligent_system()
            self._force_sync_with_trading_system()
            return result

        # PRIORIDADE 2: Auto-tuning para DESVIOS GRANDES
        if self._should_use_auto_tuning_for_large_deviations(current_pass_rate) and not force_mode:
            logger.info(" DESVIO GRANDE DETECTADO - Usando Auto-tuning Target-Driven")
            logger.info(f"   Taxa: {current_pass_rate:.1%}, Meta: 10-20%")
            optimized_thresholds = self.auto_tune_all_thresholds()

            # CORREÇÃO CRÍTICA: Sincronizar thresholds do auto-tuning com sistema de trading
            self._force_sync_auto_tuning_with_trading_system(optimized_thresholds)

            return optimized_thresholds

        # PRIORIDADE 3: Sistema Inteligente para SITUAÇÕES NORMAIS
        if self.intelligent_adaptation.config['enabled']:
            logger.info(" SITUAÇÃO NORMAL - Usando Sistema Inteligente")
            logger.info(f"   Taxa: {current_pass_rate:.1%}, Dados: {self.total_assets_analyzed}")
            result = self._adapt_with_intelligent_system()
            self._force_sync_with_trading_system()
            return result

        # PRIORIDADE 4: Sistema legado (fallback)
        logger.info(" FALLBACK - Usando sistema legado")
        return self._adapt_legacy_system(force_mode)

    def _adapt_with_intelligent_system(self):
        """Adaptação usando sistema inteligente"""
        # Calcular métricas atuais
        current_pass_rate = self.calculate_current_pass_rate()
        signals_found = 1 if current_pass_rate > 0 else 0

        # Processar ciclo no sistema inteligente
        updated_thresholds = self.intelligent_adaptation.process_cycle(
            pass_rate=current_pass_rate,
            assets_analyzed=self.total_assets_analyzed,
            signals_found=signals_found
        )

        # Atualizar current_thresholds se houve mudança
        old_thresholds = asdict(self.current_thresholds)
        new_thresholds = ThresholdConfig(
            consciousness=updated_thresholds['consciousness'],
            coherence=updated_thresholds['coherence'],
            confidence=updated_thresholds['confidence'],
            volume_surge_min=updated_thresholds['volume_surge_min'],
            momentum_min=updated_thresholds['momentum_min']
        )

        # Verificar se houve mudança
        if asdict(new_thresholds) != old_thresholds:
            self.current_thresholds = new_thresholds

            logger.info(f"[INTELLIGENT] Thresholds atualizados pelo sistema inteligente")
            logger.info(f"[INTELLIGENT] Estado atual: {self.intelligent_adaptation.current_state.value}")

            # Log das mudanças principais
            for metric in ['consciousness', 'coherence', 'confidence']:
                old_val = old_thresholds[metric]
                new_val = getattr(new_thresholds, metric)
                if abs(new_val - old_val) > 0.001:
                    change = new_val - old_val
                    logger.info(f"[INTELLIGENT]   {metric}: {old_val:.3f} -> {new_val:.3f} ({change:+.3f})")

    def _adapt_legacy_system(self, force_mode: Optional[TradingMode] = None):
        """Sistema de adaptação legado (fallback)"""
        old_thresholds = asdict(self.current_thresholds)
        old_mode = self.current_mode

        if force_mode:
            self.current_mode = force_mode
        else:
            # Lógica de adaptação inteligente baseada em taxa de aprovação
            current_pass_rate = self.calculate_current_pass_rate()

            if current_pass_rate < 0.05:  # Menos de 5% - usar ultra-otimizado
                self.current_thresholds = self.ultra_optimized_thresholds
                adaptation_reason = f"Taxa muito baixa ({current_pass_rate:.1%}) - aplicando thresholds ultra-otimizados"
                new_mode = 'ultra_optimized'

            elif current_pass_rate < 0.10:  # Menos de 10% - usar agressivo
                self.current_mode = TradingMode.AGGRESSIVE
                adaptation_reason = f"Taxa baixa ({current_pass_rate:.1%}) - modo agressivo"
                new_mode = self.current_mode.value

            elif current_pass_rate > 0.30:  # Mais de 30% - usar conservativo
                self.current_mode = TradingMode.CONSERVATIVE
                adaptation_reason = f"Taxa alta ({current_pass_rate:.1%}) - modo conservativo"
                new_mode = self.current_mode.value

            elif self.cycles_without_signals >= 5:  # Muitos ciclos sem sinais
                self.current_mode = TradingMode.AGGRESSIVE
                adaptation_reason = f"Muitos ciclos sem sinais ({self.cycles_without_signals})"
                new_mode = self.current_mode.value

            else:
                # Usar thresholds empíricos otimizados
                self.current_thresholds = self.empirical_thresholds
                adaptation_reason = f"Aplicando thresholds empíricos otimizados (taxa atual: {current_pass_rate:.1%})"
                new_mode = 'empirical_optimized'

                adaptation = {
                    'timestamp': datetime.now().isoformat(),
                    'reason': adaptation_reason,
                    'old_mode': old_mode.value,
                    'new_mode': new_mode,
                    'old_thresholds': old_thresholds,
                    'new_thresholds': asdict(self.current_thresholds),
                    'cycles_without_signals': self.cycles_without_signals,
                    'pass_rate': current_pass_rate
                }

                self.adaptation_history.append(adaptation)
                self.performance_tracking['last_adaptation'] = datetime.now()

                logger.info("APLICANDO THRESHOLDS EMPÍRICOS OTIMIZADOS")
                logger.info(f"Taxa de aprovação atual: {current_pass_rate:.1%}")
                logger.info(f"Consciousness: {old_thresholds['consciousness']:.3f} -> {self.current_thresholds.consciousness:.3f}")
                logger.info(f"Coherence: {old_thresholds['coherence']:.3f} -> {self.current_thresholds.coherence:.3f}")
                logger.info(f"Confidence: {old_thresholds['confidence']:.3f} -> {self.current_thresholds.confidence:.3f}")
                logger.info(f"Volume Surge: {old_thresholds['volume_surge_min']:.3f} -> {self.current_thresholds.volume_surge_min:.3f}")
                logger.info(f"Momentum: {old_thresholds['momentum_min']:.4f} -> {self.current_thresholds.momentum_min:.4f}")
                return
        
        # Aplicar novo modo se não foi aplicado acima
        if new_mode not in ['ultra_optimized', 'empirical_optimized']:
            self.current_thresholds = self.threshold_configs[self.current_mode]

        # Registrar adaptação
        current_pass_rate = self.calculate_current_pass_rate()
        adaptation = {
            'timestamp': datetime.now().isoformat(),
            'reason': adaptation_reason,
            'old_mode': old_mode.value,
            'new_mode': new_mode,
            'old_thresholds': old_thresholds,
            'new_thresholds': asdict(self.current_thresholds),
            'cycles_without_signals': self.cycles_without_signals,
            'pass_rate': current_pass_rate
        }

        self.adaptation_history.append(adaptation)
        self.performance_tracking['last_adaptation'] = datetime.now()

        logger.info("ADAPTAÇÃO AUTOMÁTICA DE THRESHOLDS OTIMIZADA")
        logger.info(f"Modo: {old_mode.value} -> {new_mode}")
        logger.info(f"Motivo: {adaptation_reason}")
        logger.info(f"Taxa de aprovação: {current_pass_rate:.1%}")
        logger.info(f"Consciousness: {old_thresholds['consciousness']:.3f} -> {self.current_thresholds.consciousness:.3f}")
        logger.info(f"Coherence: {old_thresholds['coherence']:.3f} -> {self.current_thresholds.coherence:.3f}")
        logger.info(f"Confidence: {old_thresholds['confidence']:.3f} -> {self.current_thresholds.confidence:.3f}")
        logger.info(f"Volume Surge: {old_thresholds['volume_surge_min']:.3f} -> {self.current_thresholds.volume_surge_min:.3f}")
        logger.info(f"Momentum: {old_thresholds['momentum_min']:.4f} -> {self.current_thresholds.momentum_min:.4f}")
    
    def reset_cycle_counter(self):
        """Reseta contador de ciclos sem sinais"""
        self.cycles_without_signals = 0
        # Resetar flag de auto-tuning para permitir nova execução no próximo ciclo
        self._auto_tuning_executed_this_cycle = False
        # Liberar controle do Sistema Inteligente se não estiver mais em emergência
        if hasattr(self, '_intelligent_system_in_control'):
            self._intelligent_system_in_control = False
            logger.info("🔓 Controle do Sistema Inteligente liberado")
        # Permitir nova adaptação no próximo ciclo
        self._adaptation_executed_this_cycle = False
    
    def increment_cycle_counter(self):
        """Incrementa contador de ciclos sem sinais"""
        self.cycles_without_signals += 1
        # Resetar flag de auto-tuning para permitir nova execução no próximo ciclo
        self._auto_tuning_executed_this_cycle = False
        # Permitir nova adaptação no próximo ciclo
        self._adaptation_executed_this_cycle = False
    
    def log_detailed_analysis(self, analyses: List[MetricAnalysis]):
        """Log detalhado de todas as análises"""
        logger.info("=" * 80)
        logger.info("ANÁLISE DETALHADA DE MÉTRICAS vs THRESHOLDS")
        logger.info("=" * 80)
        logger.info(f"Modo atual: {self.current_mode.value.upper()}")
        logger.info(f"Thresholds: C>={self.current_thresholds.consciousness:.3f}, "
                   f"Coh>={self.current_thresholds.coherence:.3f}, "
                   f"Conf>={self.current_thresholds.confidence:.3f}")
        logger.info("-" * 80)
        
        passed_count = 0
        for analysis in analyses:
            status = "PASS" if analysis.threshold_passed else "FAIL"
            logger.info(f"{status} {analysis.symbol}:")
            logger.info(f"  Consciousness: {analysis.consciousness:.3f} "
                       f"({'PASS' if analysis.consciousness >= self.current_thresholds.consciousness else 'FAIL'})")
            logger.info(f"  Coherence:     {analysis.coherence:.3f} "
                       f"({'PASS' if analysis.coherence >= self.current_thresholds.coherence else 'FAIL'})")
            logger.info(f"  Confidence:    {analysis.confidence:.3f} "
                       f"({'PASS' if analysis.confidence >= self.current_thresholds.confidence else 'FAIL'})")
            logger.info(f"  Volume Surge:  {analysis.volume_surge:.3f} "
                       f"({'PASS' if analysis.volume_surge >= self.current_thresholds.volume_surge_min else 'FAIL'})")
            logger.info(f"  Momentum:      {abs(analysis.momentum):.4f} "
                       f"({'PASS' if abs(analysis.momentum) >= self.current_thresholds.momentum_min else 'FAIL'})")
            logger.info(f"  Quality Score: {analysis.quality_score:.3f}")

            # Adicionar combined_score com threshold de aprovação
            min_combined_score = getattr(self, 'trading_system', None)
            if min_combined_score and hasattr(min_combined_score, 'config'):
                min_combined_score = min_combined_score.config.get('signal_quality_filters', {}).get('min_combined_score', 0.65)
            else:
                min_combined_score = 0.65  # Fallback padrão

            combined_status = "PASS" if analysis.combined_score >= min_combined_score else "FAIL"
            logger.info(f"  Combined Score: {analysis.combined_score:.3f} ({combined_status} vs {min_combined_score:.2f} threshold)")

            if not analysis.threshold_passed:
                logger.info(f"  Failed: {', '.join(analysis.failed_thresholds)}")
            else:
                passed_count += 1

            logger.info("")
        
        pass_rate = passed_count / len(analyses) * 100 if analyses else 0

        # CORREÇÃO: Armazenar taxa para uso consistente em decisões de adaptação
        self._last_detailed_pass_rate = pass_rate / 100  # Converter para decimal (0.19 ao invés de 19.0)

        logger.info(f"RESUMO: {passed_count}/{len(analyses)} ativos passaram ({pass_rate:.1f}%)")
        logger.info(f"META: 10-20% de aprovação")
        logger.info(f"Ciclos sem sinais: {self.cycles_without_signals}")

        # Sugestão de otimização baseada na taxa atual
        if pass_rate < 8:
            logger.warning(f"TAXA MUITO BAIXA ({pass_rate:.1f}%) - Thresholds muito restritivos")
        elif pass_rate > 25:
            logger.warning(f"TAXA ALTA ({pass_rate:.1f}%) - Thresholds muito permissivos")
        else:
            logger.info(f"TAXA ADEQUADA: {pass_rate:.1f}% está próxima da meta (10-20%)")

        logger.info("=" * 80)

    def get_optimization_summary(self) -> Dict:
        """Retorna resumo das otimizações implementadas"""
        current_pass_rate = self.calculate_current_pass_rate()

        return {
            'optimization_status': 'IMPLEMENTED',
            'current_mode': self.current_mode.value,
            'current_pass_rate': current_pass_rate,
            'target_pass_rate': '10-20%',
            'optimizations': {
                'moderate_mode': {
                    'volume_surge_min': f"1.3 -> {self.threshold_configs[TradingMode.MODERATE].volume_surge_min}",
                    'momentum_min': f"0.0008 -> {self.threshold_configs[TradingMode.MODERATE].momentum_min}",
                    'consciousness': f"0.60 -> {self.threshold_configs[TradingMode.MODERATE].consciousness}",
                    'coherence': f"0.50 -> {self.threshold_configs[TradingMode.MODERATE].coherence}",
                    'confidence': f"0.55 -> {self.threshold_configs[TradingMode.MODERATE].confidence}"
                },
                'aggressive_mode': {
                    'volume_surge_min': f"1.2 -> {self.threshold_configs[TradingMode.AGGRESSIVE].volume_surge_min}",
                    'momentum_min': f"0.0005 -> {self.threshold_configs[TradingMode.AGGRESSIVE].momentum_min}"
                },
                'ultra_optimized': {
                    'volume_surge_min': self.ultra_optimized_thresholds.volume_surge_min,
                    'momentum_min': self.ultra_optimized_thresholds.momentum_min,
                    'description': 'Para situações de baixa atividade'
                }
            },
            'intelligent_adaptation': {
                'pass_rate_monitoring': True,
                'automatic_adjustment': True,
                'target_achievement': current_pass_rate >= 0.10 and current_pass_rate <= 0.20
            },
            'total_adaptations': len(self.adaptation_history),
            'cycles_without_signals': self.cycles_without_signals
        }

    def _force_sync_with_trading_system(self):
        """
        Força sincronização dos thresholds com o sistema de trading
        Garante que mudanças do sistema inteligente sejam aplicadas efetivamente
        """
        try:
            if hasattr(self, 'trading_system') and self.trading_system:
                # Obter thresholds atuais do sistema inteligente
                intelligent_thresholds = self.intelligent_adaptation.current_thresholds

                # Aplicar no sistema de trading
                self.trading_system.quantum_thresholds.update(intelligent_thresholds)

                # Sincronizar com current_thresholds local
                self.current_thresholds = ThresholdConfig(
                    consciousness=intelligent_thresholds['consciousness'],
                    coherence=intelligent_thresholds['coherence'],
                    confidence=intelligent_thresholds['confidence'],
                    volume_surge_min=intelligent_thresholds['volume_surge_min'],
                    momentum_min=intelligent_thresholds['momentum_min']
                )

                logger.info("Thresholds sincronizados com sistema de trading")

                # Log das mudanças para debug
                for metric, value in intelligent_thresholds.items():
                    logger.debug(f"   {metric}: {value:.4f}")

            else:
                logger.warning("Sistema de trading não disponível para sincronização")

        except Exception as e:
            logger.error(f"Erro na sincronização com sistema de trading: {e}")

    def _force_sync_auto_tuning_with_trading_system(self, optimized_thresholds: Dict[str, float]):
        """
        CORREÇÃO CRÍTICA: Força sincronização dos thresholds do auto-tuning
        Garante que mudanças do auto-tuning sejam persistidas no sistema de trading
        """
        try:
            if hasattr(self, 'trading_system') and self.trading_system:
                # Aplicar thresholds otimizados no sistema de trading
                self.trading_system.quantum_thresholds.update(optimized_thresholds)

                # CRÍTICO: Atualizar current_thresholds local para persistir entre ciclos
                for metric, value in optimized_thresholds.items():
                    if hasattr(self.current_thresholds, metric):
                        setattr(self.current_thresholds, metric, value)

                # CRÍTICO: Atualizar sistema inteligente para manter consistência
                if hasattr(self, 'intelligent_adaptation'):
                    self.intelligent_adaptation.current_thresholds.update(optimized_thresholds)

                logger.info(" Auto-tuning thresholds sincronizados com sistema de trading")

                # Log detalhado das mudanças persistidas
                for metric, value in optimized_thresholds.items():
                    logger.info(f"   {metric}: {value:.4f} (PERSISTIDO)")

            else:
                logger.error(" Sistema de trading não disponível - thresholds NÃO persistidos")

        except Exception as e:
            logger.error(f" ERRO CRÍTICO na sincronização auto-tuning: {e}")
            logger.error("   Thresholds podem não ter sido persistidos!")

    def tune_to_target(self, metric_name: str, start: float, direction: str,
                      eval_fn, target_low: float = 0.10, target_high: float = 0.20) -> float:
        """
        Algoritmo de busca para ajustar thresholds até atingir meta de 10-20% de aprovação

        Args:
            metric_name: Nome da métrica a ser ajustada
            start: Valor inicial do threshold
            direction: "down" para relaxar, "up" para restringir
            eval_fn: Função que avalia taxa de aprovação para um threshold
            target_low: Taxa mínima de aprovação (10%)
            target_high: Taxa máxima de aprovação (20%)

        Returns:
            Threshold otimizado
        """
        logger.info(f" Iniciando busca target-driven para {metric_name}")
        logger.info(f"   Meta: {target_low:.0%}-{target_high:.0%} de aprovação")
        logger.info(f"   Valor inicial: {start:.4f}")

        # Definir bounds iniciais
        if direction == "down":
            lo, hi = start * 0.3, start  # Relaxar: valores menores
        else:
            lo, hi = start, start * 1.7  # Restringir: valores maiores

        best_threshold = start
        best_rate = eval_fn(metric_name, start)

        logger.info(f"   Taxa inicial: {best_rate:.1%}")

        # Busca binária otimizada
        for iteration in range(8):
            mid = (lo + hi) / 2
            rate = eval_fn(metric_name, mid)

            logger.debug(f"   Iteração {iteration+1}: threshold={mid:.4f}, taxa={rate:.1%}")

            # Verificar se atingiu o target
            if target_low <= rate <= target_high:
                logger.info(f"    Target atingido! Threshold={mid:.4f}, Taxa={rate:.1%}")
                return mid

            # Ajustar bounds
            if rate < target_low:
                # Taxa muito baixa, precisa relaxar mais (threshold menor)
                hi = mid
                if direction == "down":
                    lo = lo * 0.7  # Relaxar mais
            else:
                # Taxa muito alta, precisa restringir mais (threshold maior)
                lo = mid
                if direction == "up":
                    hi = hi * 0.9  # Restringir mais

            # Atualizar melhor resultado
            if abs(rate - (target_low + target_high) / 2) < abs(best_rate - (target_low + target_high) / 2):
                best_threshold = mid
                best_rate = rate

        logger.info(f"    Melhor resultado: threshold={best_threshold:.4f}, taxa={best_rate:.1%}")
        return best_threshold

    def auto_tune_all_thresholds(self) -> Dict[str, float]:
        """
        Auto-ajusta TODOS os thresholds para atingir meta de 10-20% de aprovação

        Returns:
            Dicionário com thresholds otimizados
        """
        logger.info(" Iniciando auto-tuning de TODOS os thresholds")
        logger.info(" Meta: 10-20% de aprovação")

        # Marcar que auto-tuning foi executado neste ciclo (prevenir loop infinito)
        self._auto_tuning_executed_this_cycle = True

        if not self.metric_statistics['consciousness']:
            logger.warning(" Sem dados suficientes para auto-tuning")
            return asdict(self.current_thresholds)

        # CORREÇÃO CRÍTICA: Função de avaliação que simula taxa de aprovação COMBINADA
        def evaluate_threshold(metric_name: str, threshold_value: float) -> float:
            """Simula taxa de aprovação COMBINADA com novo threshold"""
            if not self.metric_statistics['consciousness']:
                return 0.0

            total_assets = len(self.metric_statistics['consciousness'])
            passed_assets = 0

            # Criar thresholds temporários com o novo valor
            temp_thresholds = {
                'consciousness': self.current_thresholds.consciousness,
                'coherence': self.current_thresholds.coherence,
                'confidence': self.current_thresholds.confidence,
                'volume_surge_min': self.current_thresholds.volume_surge_min,
                'momentum_min': self.current_thresholds.momentum_min
            }

            # Atualizar o threshold sendo testado
            if metric_name == 'volume_surge':
                temp_thresholds['volume_surge_min'] = threshold_value
            elif metric_name == 'momentum':
                temp_thresholds['momentum_min'] = threshold_value
            else:
                temp_thresholds[metric_name] = threshold_value

            # Calcular quantos ativos passam em TODAS as métricas
            for i in range(total_assets):
                consciousness_pass = (i < len(self.metric_statistics['consciousness']) and
                                    self.metric_statistics['consciousness'][i] >= temp_thresholds['consciousness'])

                coherence_pass = (i < len(self.metric_statistics['coherence']) and
                                self.metric_statistics['coherence'][i] >= temp_thresholds['coherence'])

                confidence_pass = (i < len(self.metric_statistics['confidence']) and
                                 self.metric_statistics['confidence'][i] >= temp_thresholds['confidence'])

                volume_pass = True
                if 'volume_surge' in self.metric_statistics and i < len(self.metric_statistics['volume_surge']):
                    volume_pass = self.metric_statistics['volume_surge'][i] >= temp_thresholds['volume_surge_min']

                momentum_pass = True
                if 'momentum' in self.metric_statistics and i < len(self.metric_statistics['momentum']):
                    momentum_pass = abs(self.metric_statistics['momentum'][i]) >= temp_thresholds['momentum_min']

                if consciousness_pass and coherence_pass and confidence_pass and volume_pass and momentum_pass:
                    passed_assets += 1

            return passed_assets / total_assets if total_assets > 0 else 0.0

        optimized_thresholds = {}

        # Otimizar cada métrica
        metrics_to_tune = [
            ('consciousness', 'down'),
            ('coherence', 'down'),
            ('confidence', 'down'),
            ('volume_surge', 'down'),
            ('momentum', 'down')
        ]

        for metric_name, direction in metrics_to_tune:
            if metric_name in self.metric_statistics and self.metric_statistics[metric_name]:
                # Mapear nome da métrica para o atributo correto do ThresholdConfig
                if metric_name == 'volume_surge':
                    threshold_attr = 'volume_surge_min'
                elif metric_name == 'momentum':
                    threshold_attr = 'momentum_min'
                else:
                    threshold_attr = metric_name

                current_value = getattr(self.current_thresholds, threshold_attr)

                optimized_value = self.tune_to_target(
                    metric_name=metric_name,
                    start=current_value,
                    direction=direction,
                    eval_fn=evaluate_threshold,
                    target_low=0.10,
                    target_high=0.20
                )

                # Mapear nome correto
                threshold_key = metric_name if metric_name != 'volume_surge' else 'volume_surge_min'
                threshold_key = threshold_key if threshold_key != 'momentum' else 'momentum_min'

                optimized_thresholds[threshold_key] = optimized_value

                # Log da otimização
                change_pct = ((optimized_value - current_value) / current_value) * 100 if current_value != 0 else 0
                logger.info(f"   {threshold_key}: {current_value:.4f} -> {optimized_value:.4f} ({change_pct:+.1f}%)")

        # Aplicar thresholds otimizados
        if optimized_thresholds:
            logger.info(" Aplicando thresholds auto-otimizados")

            # CORREÇÃO: Salvar thresholds antigos ANTES de atualizar
            old_thresholds_dict = asdict(self.current_thresholds)

            # Atualizar current_thresholds
            for key, value in optimized_thresholds.items():
                if hasattr(self.current_thresholds, key):
                    setattr(self.current_thresholds, key, value)
                    logger.debug(f"    {key} atualizado: {value:.4f}")

            # NOTA: Sincronização será feita pelo método _force_sync_auto_tuning_with_trading_system
            # que é chamado após este método retornar

            # Registrar adaptação com thresholds corretos
            self.adaptation_history.append({
                'timestamp': datetime.now().isoformat(),
                'reason': 'Auto-tuning para meta 10-20%',
                'method': 'target_driven_search',
                'old_thresholds': old_thresholds_dict,  # Thresholds antes da mudança
                'new_thresholds': optimized_thresholds,
                'target_range': '10-20%'
            })

        logger.info(" Auto-tuning concluído!")
        return optimized_thresholds
